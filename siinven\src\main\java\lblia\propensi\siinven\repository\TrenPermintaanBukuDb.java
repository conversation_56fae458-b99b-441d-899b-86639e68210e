package lblia.propensi.siinven.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import lblia.propensi.siinven.model.TrenPermintaanBuku;

@Repository
public interface TrenPermintaanBukuDb extends JpaRepository<TrenPermintaanBuku, Long> {

    @Query("""
        SELECT p FROM TrenPermintaanBuku p
        WHERE p.kategoriBarang = 'Buku'
        AND p.tanggalPemesanan BETWEEN :startDate AND :endDate
        AND (:nomorCabang IS NULL OR p.nomorCabang = :nomorCabang)
    """)
    List<TrenPermintaanBuku> findByFilters(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        @Param("nomorCabang") String nomorCabang
    );

    @Query("""
        SELECT p FROM TrenPermintaanBuku p
        WHERE p.kategoriBarang = 'Buku'
        AND p.tanggalPemesanan BETWEEN :startDate AND :endDate
    """)
    List<TrenPermintaanBuku> findByFiltersAllCabang(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

}
