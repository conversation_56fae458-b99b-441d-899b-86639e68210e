package lblia.propensi.siinven;

import com.github.javafaker.Faker;
import lblia.propensi.siinven.dto.response.CabangAsliResponseDTO;
import lblia.propensi.siinven.model.*;
import lblia.propensi.siinven.repository.*;
import lblia.propensi.siinven.service.*;
import org.springframework.boot.CommandLineRunner;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.NotifikasiResponseDTO;

import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@SpringBootApplication
@RestController
public class SiinvenApplication {

    @Autowired
    private StokCabangService stokCabangService;

    @Autowired
    private NotifikasiDb notifikasiDb;

    @RequestMapping("/")
    public String home() {
        return "Hello World";
    }


    private String getRandomRole() {
        String[] roles = {
            "Staf Gudang Pelaksana Umum",
            "Staf Inventarisasi",
            "Staf Pengadaan dan Pembelian",
            "Kepala Departemen SDM dan Umum",
            "Direktur Utama",
            "Staf keuangan",
            "Kepala Operasional Cabang"
        };
        int randomIndex = new Random().nextInt(roles.length);
        return roles[randomIndex];
    }

    public static void main(String[] args) {
        SpringApplication.run(SiinvenApplication.class, args);
    }

    @Bean
    @Transactional
    CommandLineRunner run(PenggunaDb penggunaDb, StokBarangService stokBarangService,
                          CabangAsliRestServiceImpl cabangAsliService,
                          UserRestService userRestService, StokCabangService stokCabangService,
                          CopyBarangDb copyBarangDb, NotifikasiDb notifikasiDb, BarangDb barangDb,
                          CabangAsliDb cabangAsliDb, CabangKerjaSamaDb cabangKerjaSamaDb,
                          StokBarangRepository stokBarangRepository,
                          TrenPermintaanBukuDb trenPermintaanBukuDb) {
        return args -> {

            // Create admin user
            Pengguna pengguna1 = new Pengguna();
            pengguna1.setIdKaryawan("AdminID");
            pengguna1.setUsername("admin");
            pengguna1.setPassword(userRestService.hashPassword("admin1234"));
            pengguna1.setRole("Admin");
            pengguna1.setEmail("<EMAIL>");
            pengguna1.setNomorTelepon("08123456789");
            pengguna1.setNamaLengkap("Admin");
            pengguna1.setNomorCabang("001");
            penggunaDb.save(pengguna1);
            System.out.println("User Admin" + ": " + pengguna1.getUsername() + " - Role: " + pengguna1.getRole() + " - Cabang: " + pengguna1.getNomorCabang() + " - Password: admin1234");

            Random random = new Random();
            Faker faker = new Faker();
            List<String> roles = Arrays.asList(
                    "Staf Gudang Pelaksana Umum",
                    "Staf Inventarisasi",
                    "Staf Pengadaan dan Pembelian",
                    "Kepala Departemen SDM dan Umum",
                    "Direktur Utama",
                    "Staf keuangan"
            );

            for (int i = 0; i < roles.size(); i++) {
                Pengguna pengguna = new Pengguna();
                pengguna.setIdKaryawan("pengguna" + i);
                pengguna.setUsername("pengguna" + i);
                pengguna.setNamaLengkap(faker.name().fullName());
                pengguna.setPassword(userRestService.hashPassword("pengguna" + i));
                String role = roles.get(i);
                pengguna.setRole(role);
                pengguna.setEmail(faker.internet().emailAddress());
                pengguna.setNomorTelepon(faker.phoneNumber().cellPhone());
                pengguna.setNomorCabang("001");

                penggunaDb.save(pengguna);

                System.out.println("User " + i + ": " + pengguna.getUsername() + " - Role: " + role + " - Cabang: " + pengguna.getNomorCabang() + " - Password: " + pengguna.getUsername());
            }

            for (int i = 1; i <= 15; i++) {
                Barang barang = new Barang();

                barang.setNamaBarang("Barang " + i);
                barang.setHargaBarang(random.nextDouble(10000, 100000));
                barang.setKategoriBarang(faker.commerce().department());
                barang.setBentuk(faker.options().option("satuan", "paket"));

                barangDb.save(barang);
            }


            /** Start
             * Menambahkan data Barang
             * */

            List<Map<String, Object>> listBarang = new ArrayList<>();
            Map<String, Object> barang;

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 1 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 1 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 1 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 2 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 2 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 2 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 3 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 3 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 3 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 4 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 4 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Rainbow 4 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 1 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 1 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 1 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 2 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 2 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 2 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 3 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 3 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 3 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 4 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 4 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Star 4 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 1 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 1 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 1 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 2 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 2 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 2 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 3 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 3 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 3 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 4 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 4 Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 50000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Comet 4 Student & Exercise Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 120000);
            barang.put("bentuk", "paket");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Cool Way 1 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Cool Way 2 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Cool Way 3 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Cool Way 4 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Active Way 1 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Active Way 2 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Active Way 3 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Active Way 4 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Creative Way 1 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "The Creative Way 2 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Students 1 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Students 2 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Students 3 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Students 4 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Business Beginner");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Business Elementary");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Business Pre Intermediate");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Business Intermediate");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Business Upper Intermediate");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Conversation for Business Advanced");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "TOEFL® Preparation 1 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "TOEFL® Preparation 2 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "TOEFL® Preparation 3 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "TOEFL® Preparation 4 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "IELTS™ Preparation 1");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "IELTS™ Preparation 2");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "IELTS™ Preparation 3");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "TOEIC® Preparation 1 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "TOEIC® Preparation 2 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "TOEIC® Preparation 3 Student Book");
            barang.put("kategoriBarang", "Buku");
            barang.put("hargaBarang", 80000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Spidol Hitam Whiteboard Snowman");
            barang.put("kategoriBarang", "Alat Tulis Kantor");
            barang.put("hargaBarang", 10000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Spidol Biru Whiteboard Snowman");
            barang.put("kategoriBarang", "Alat Tulis Kantor");
            barang.put("hargaBarang", 10000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Spidol Merah Whiteboard Snowman");
            barang.put("kategoriBarang", "Alat Tulis Kantor");
            barang.put("hargaBarang", 10000);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Penghapus Whiteboard Gunindo");
            barang.put("kategoriBarang", "Alat Tulis Kantor");
            barang.put("hargaBarang", 8500);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Staples Joyko HD-10");
            barang.put("kategoriBarang", "Alat Tulis Kantor");
            barang.put("hargaBarang", 7500);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Isi Staples No.10-1M Kenko 1000 Staples");
            barang.put("kategoriBarang", "Alat Tulis Kantor");
            barang.put("hargaBarang", 1500);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Kertas HVS A4 80 Gram Bola Dunia 500 Lembar");
            barang.put("kategoriBarang", "Alat Tulis Kantor");
            barang.put("hargaBarang", 53500);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Kertas HVS F4 80 Gram Bola Dunia 500 Lembar");
            barang.put("kategoriBarang", "Alat Tulis Kantor");
            barang.put("hargaBarang", 58500);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            barang = new HashMap<>();
            barang.put("namaBarang", "Kertas HVS Q 80 Gram Bola Dunia 500 Lembar");
            barang.put("kategoriBarang", "Alat Tulis Kantor");
            barang.put("hargaBarang", 51500);
            barang.put("bentuk", "satuan");
            listBarang.add(barang);

            for (Map<String, Object> b : listBarang) {
                Barang barangToSave = new Barang();
                barangToSave.setNamaBarang(b.get("namaBarang").toString());
                barangToSave.setKategoriBarang(b.get("kategoriBarang").toString());
                barangToSave.setHargaBarang(Double.parseDouble(b.get("hargaBarang").toString()));
                barangToSave.setBentuk(b.get("bentuk").toString());

                barangDb.save(barangToSave);
            }

            /** Menambahkan data Barang
             * Selesai
             * */



            List<Map<String, String>> daftarCabangAsli = new ArrayList<>();
            Map<String, String> cabAsli;

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA PENGADEGAN - Jakarta Selatan");
            cabAsli.put("alamat", "Jl.Pengadegan Timur No.3 Pancoran, Jakarta Selatan, DKI Jakarta");
            cabAsli.put("kontak", "0881-5353-031, 0878-8801-0322");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA PASAR MINGGU - Jakarta Selatan");
            cabAsli.put("alamat", "Jl. Raya Rawa Bambu No.19A, RT.13/RW.5, Pasar Minggu, Jakarta Selatan, DKI Jakarta");
            cabAsli.put("kontak", "0813-1415-7476");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA FATMAWATI - Jakarta Selatan");
            cabAsli.put("alamat", "Komp. Fatmawati Mas Blok 1/101-102, Jl. RS Fatmawati , Cilandak, Jakarta Selatan, DKI Jakarta");
            cabAsli.put("kontak", "0877-7848-3779");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA PRAMUKA - Jakarta Timur");
            cabAsli.put("alamat", "Jl. Pramuka No.Kavling 30, RT.11/RW.5, Utan Kayu Utara, Kec. Matraman, Jakarta Timur, DKI Jakarta");
            cabAsli.put("kontak", "0812-8888-3241");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA DUREN SAWIT - Jakarta Timur");
            cabAsli.put("alamat", "Jl. Pahlawan Revolusi No.4, RT 3/RW 4, Klender, Kec. Duren Sawit, Jakarta Timur, DKI Jakarta");
            cabAsli.put("kontak", "0813-1557-0101");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA GROGOL - Khusus Online");
            cabAsli.put("alamat", "Jl.Pengadegan Timur No.3 Pancoran, Jakarta Selatan, DKI Jakarta");
            cabAsli.put("kontak", "0812-2020-8984");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA CENGKARENG - Jakarta Barat");
            cabAsli.put("alamat", "Plaza De Lumina Blok C-3/A, Taman Semanan Indah Jl. Lingkar Luar Barat, Cengkareng, Jakarta Barat, DKI Jakarta");
            cabAsli.put("kontak", "0838-9147-4399");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA KALIDERES - Jakarta Barat");
            cabAsli.put("alamat", "Komplek Pertokoan Citra Business Park Blok A No. 20-21-22 Jl. Peta Barat – Kalideres, Jakarta Barat, DKI Jakarta");
            cabAsli.put("kontak", "0815-1395-8897");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA MERUYA - Jakarta Barat");
            cabAsli.put("alamat", "Jl. H. Lebar No.36E RT. 007/02 Meruya Selatan Kec. Kembangan, Jakarta Barat, DKI Jakarta");
            cabAsli.put("kontak", "0812-5000-6299");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA KELAPA GADING - Jakarta Utara");
            cabAsli.put("alamat", "Jl. Boulevard Artha Gading, Rukan Gading Bukit Indah Blok P No. 24 - 25 Kelapa Gading, Jakarta Utara, DKI Jakarta");
            cabAsli.put("kontak", "0812-1111-9219");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA CILEDUG - Tangerang");
            cabAsli.put("alamat", "Jl. HOS Cokroaminoto 93, Ruko CBD Mall Blok A3 No. 1-2 & 36 Karang Tengah, Ciledug, Tangerang, Banten");
            cabAsli.put("kontak", "0899-4310-709");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA CIKOKOL - Tangerang");
            cabAsli.put("alamat", "Komp. Pertokoan Mahkota Mas Blok B2-B6 Jl. MH. Thamrin - Cikokol, Tangerang, Banten");
            cabAsli.put("kontak", "0815-8432-2646");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA KARAWACI - Tangerang");
            cabAsli.put("alamat", "Jl. Borobudur Raya no. 147 Perumnas II Tangerang, Tangerang, Banten");
            cabAsli.put("kontak", "0815-8432-2646");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA BSD - Tangerang Selatan");
            cabAsli.put("alamat", "Ruko Golden Madrid Blok D/17 Jl. Letnan Sutopo, BSD, Tangerang Selatan, Banten");
            cabAsli.put("kontak", "0815-8432-2646");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA CIPUTAT - Tangerang Selatan");
            cabAsli.put("alamat", "Plaza Ciputat Mas Kav. C, Jl. Ir. H. Juanda 5A, Ciputat 15412, Tangerang Selatan, Banten");
            cabAsli.put("kontak", "0811-9740-744");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA PAMULANG - Tangerang Selatan");
            cabAsli.put("alamat", "Jl. RE. Martadinata KM. 8 Pamulang, Tangerang Selatan, Banten");
            cabAsli.put("kontak", "0878-7762-6777");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA BINTARO - Tangerang Selatan");
            cabAsli.put("alamat", "Distrik 9, Jl. Jend. Sudirman (Boulevard) Blok B No. 5,6 & 8, Bintaro Jaya, Tangerang Selatan, Banten");
            cabAsli.put("kontak", "0811-9740-744");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA LINGKAR SELATAN - Serang");
            cabAsli.put("alamat", "Jl. Mayor HM. Muslih No.49, Lingkar Selatan – Serang, Banten");
            cabAsli.put("kontak", "0812-1041-0021");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA RAYA MERAK - Cilegon");
            cabAsli.put("alamat", "Ruko Jombang Business Center A5-7, Jl. Temu Putih, Cilegon, Banten");
            cabAsli.put("kontak", "0822-9801-7811");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA CINERE - Depok");
            cabAsli.put("alamat", "Perumahan Mega Cinere, Jl. Jakarta No. 47, Depok, Jawa Barat");
            cabAsli.put("kontak", "0811-9740-744");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA MARGONDA - Depok");
            cabAsli.put("alamat", "Jl. Margonda Raya No. 200, Depok, Jawa Barat");
            cabAsli.put("kontak", "0811-1170-217");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA MUTIARA GADING TIMUR - Bekasi");
            cabAsli.put("alamat", "Ruko Sun Flower Blok R 04 No. 21- 23, Perumahan Mutiara Gading Timur 2, Mustika Jaya, Bekasi, Jawa Barat");
            cabAsli.put("kontak", "0895-1527-7755");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA GALAXY - Bekasi");
            cabAsli.put("alamat", "Jl.Galaxy Raya No. 1-6 Jaka Sampurna, Bekasi, Jawa Barat");
            cabAsli.put("kontak", "0822-4942-2219, 0852-1777-0822");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA CIBUBUR – Bekasi");
            cabAsli.put("alamat", "Jl. Alternatif Cibubur No.6, RT.003/RW.018, Kec. Jatisampurna, Bekasi, Jawa Barat");
            cabAsli.put("kontak", "0895-3274-73902, 0882-9786-5072");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA HARAPAN INDAH – Bekasi");
            cabAsli.put("alamat", "Jl. Raya Harapan Indah, Ruko Sentra Niaga Blok SN Harapan Indah, Bekasi, Jawa Barat");
            cabAsli.put("kontak", "0822-4617-1624");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA PAJAJARAN – Bogor");
            cabAsli.put("alamat", "Mall Lippo Plaza Kebon Raya UG 01 Jl. Pajajaran No 27, Bogor, Jawa Barat");
            cabAsli.put("kontak", "0888-0910-1384");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA JALAN BARU – Bogor");
            cabAsli.put("alamat", "Komp. Ruko 24 Jl. KH. Sholeh Iskandar No. 2, Bogor, Jawa Barat");
            cabAsli.put("kontak", "0878-8045-5648");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA CIBINONG – Bogor");
            cabAsli.put("alamat", "Graha Pratama Blok F 3 – 6 Jl. Tegar Beriman, Bogor, Jawa Barat");
            cabAsli.put("kontak", "0852-1583-6115");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA PODOMORO GOLF VIEW (PGV) – Bogor");
            cabAsli.put("alamat", "Ruko PGV Blok B2 52-56, Bojong Nangka, Gn. Putri, Bogor, Jawa Barat");
            cabAsli.put("kontak", "0877-4228-0313");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA Citra Indah City - Bogor");
            cabAsli.put("alamat", "Jl. Citra Indah Ruko Shopping St No.08/18, Sukamaju, Kec. Jonggol, Bogor, Jawa Barat");
            cabAsli.put("kontak", "0813-8998-6060");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA KARAWANG - Karawang");
            cabAsli.put("alamat", "Jl. Husni Hamid No. 12 Kel. Nagasari, Kec. Karawang Barat, Karawang, Jawa Barat");
            cabAsli.put("kontak", "0811-887-200");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA METRO - Bandung");
            cabAsli.put("alamat", "Metro Trade Center (MTC) Blok A No. 8, Jl. Soekarno - Hatta, Buah Batu, Bandung, Jawa Barat");
            cabAsli.put("kontak", "0857-4517-0301");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA MARTADINATA – Bandung");
            cabAsli.put("alamat", "Jl. R.E. Martadinata No.104, Bandung, Jawa Barat");
            cabAsli.put("kontak", "0852-1111-7886");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA CIREBON - Cirebon");
            cabAsli.put("alamat", "Jl. Kesambi No.83, Drajat, Kec. Kesambi, Kota Cirebon, Jawa Barat");
            cabAsli.put("kontak", "0813-2027-9990");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA CIMAHI - Cimahi");
            cabAsli.put("alamat", "Jl. Sangkuriang No.92, RT.03/RW.02, Cipageran, Kec. Cimahi Utara, Cimahi, Jawa Barat");
            cabAsli.put("kontak", "0821-3663-6604");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA BHAYANGKARA - Surakarta");
            cabAsli.put("alamat", "Jl. Bhayangkara No 10 Surakarta, Jawa Tengah");
            cabAsli.put("kontak", "0856-4519-0222");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA SUGIYOPRANOTO - Semarang");
            cabAsli.put("alamat", "Jl. Sugiyopranoto No. 48 RT 001 RW.001 Kel. Bulustalan, Kec. Semarang Selatan, Semarang, Jawa Tengah");
            cabAsli.put("kontak", "0818-0975-8399");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA PURWOKERTO");
            cabAsli.put("alamat", "Jl. Overste Isdiman II No.1 Purwokerto Timur, Purwokerto, Jawa Tengah");
            cabAsli.put("kontak", "0823-2405-3804");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LIA YOGYAKARTA-PANDEANSARI");
            cabAsli.put("alamat", "Jl. Lingkar Utara, Pandeansari IV/8 Condong Catur, Yogyakarta 55283");
            cabAsli.put("kontak", "082260862122, (0274) 885 816");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA MALANG");
            cabAsli.put("alamat", "Jl. Bandung No. 1A Malang, Jawa Timur");
            cabAsli.put("kontak", "0856-0853-5577");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA NGAGEL - Surabaya");
            cabAsli.put("alamat", "Jl. Ngagel Jaya No.8, Surabaya, Jawa Timur");
            cabAsli.put("kontak", "0821-4141-8272");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA GAYUNG SARI - Surabaya");
            cabAsli.put("alamat", "Jl. Gayung Sari 1 No. 79 Surabaya, Jawa Timur");
            cabAsli.put("kontak", "0896-7821-0156");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA GATOT SUBROTO - Banjarmasin");
            cabAsli.put("alamat", "Jl. Gatot Subroto No.1 Banjarmasin, Kalimantan Selatan");
            cabAsli.put("kontak", "0815-2044-7474");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA BANJARBARU - Banjarbaru");
            cabAsli.put("alamat", "Jl. Mawar Utara No. 2, RT.01, Banjarbaru, Kalimantan Selatan");
            cabAsli.put("kontak", "0853-9091-0843");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA RATULANGI - Makassar");
            cabAsli.put("alamat", "Jl. Ratulangi No. 87, Makassar, Sulawesi Selatan");
            cabAsli.put("kontak", "0812-5316-6872");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA PADANG");
            cabAsli.put("alamat", "Jl. Aur No. 18, Padang, Sumatera Barat");
            cabAsli.put("kontak", "0821-7163-5330");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA ADINEGORO - Bukittinggi");
            cabAsli.put("alamat", "Jl. Adinegoro 20 Tangah Juo, Birugo, Bukittinggi, Sumatera Barat");
            cabAsli.put("kontak", "0852-7296-4454");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA PAYAKUMBUH");
            cabAsli.put("alamat", "Jl. Sudirman No. 19 (di samping SMPN 1 Payakumbuh), Payakumbuh, Sumatera Barat");
            cabAsli.put("kontak", "0813-7472-2266");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA MEDAN");
            cabAsli.put("alamat", "Jl. Iskandar Muda No.38 C Medan, Sumatera Utara");
            cabAsli.put("kontak", "0812-6439-1731");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA AHMAD YANI - Pekanbaru");
            cabAsli.put("alamat", "Jl. Jend. A. Yani No. 149, Pekanbaru, Riau");
            cabAsli.put("kontak", "0852-7275-2792");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA SUDIRMAN - Palembang");
            cabAsli.put("alamat", "Jl. Jend. Sudirman No. 2953, Palembang, Sumatera Selatan");
            cabAsli.put("kontak", "0812-1141-1401");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA TELANAI PURA - Jambi");
            cabAsli.put("alamat", "Jl. H.M. Yusuf Singedekane No. 2, Danau Sipin, Jambi, Jambi");
            cabAsli.put("kontak", "0812-7255-4022");
            daftarCabangAsli.add(cabAsli);

            cabAsli = new HashMap<>();
            cabAsli.put("namaCabang", "LB LIA KARTINI - Bandar Lampung");
            cabAsli.put("alamat", "Jl. Kartini 40, Tanjung Karang, Bandar Lampung, Lampung");
            cabAsli.put("kontak", "0853-6606-2934");
            daftarCabangAsli.add(cabAsli);

            for (int i = 0; i < daftarCabangAsli.size(); i++) {
                Map<String, String> cabAsliMap = daftarCabangAsli.get(i);

                CabangAsli branch = new CabangAsli();
                branch.setNomorCabang(String.format("%03d", i + 2));
                branch.setNamaCabang(cabAsliMap.get("namaCabang"));
                branch.setAlamat(cabAsliMap.get("alamat"));
                branch.setKontak(cabAsliMap.get("kontak"));
                branch.setJumlahKaryawan(30);
                branch.setJamOperasional(new Time(8, 0, 0));
                branch.setIdKepalaOperasional("12345" + String.format("%03d", i + 2));
                cabangAsliDb.save(branch);

                Pengguna kepalaOperasionalCabang = new Pengguna();
                kepalaOperasionalCabang.setIdKaryawan("12345" + String.format("%03d", i + 2));
                kepalaOperasionalCabang.setUsername("kepala" + String.format("%03d", i + 2));
                kepalaOperasionalCabang.setNamaLengkap(faker.name().fullName());
                kepalaOperasionalCabang.setPassword(userRestService.hashPassword("kepala" + String.format("%03d", i + 2)));
                kepalaOperasionalCabang.setRole("Kepala Operasional Cabang");
                kepalaOperasionalCabang.setEmail(faker.internet().emailAddress());
                kepalaOperasionalCabang.setNomorTelepon(faker.phoneNumber().cellPhone());
                kepalaOperasionalCabang.setNomorCabang(branch.getNomorCabang());

                branch.setIdKepalaOperasional(kepalaOperasionalCabang.getIdKaryawan());

                System.out.println("User " + i + ": " + kepalaOperasionalCabang.getUsername() + " - Role: " + kepalaOperasionalCabang.getRole() + " - Cabang: " + kepalaOperasionalCabang.getNomorCabang() + " - Password: " + kepalaOperasionalCabang.getUsername());
                penggunaDb.save(kepalaOperasionalCabang);
                cabangAsliDb.save(branch);
            }

            List<Map<String, String>> cksList = new ArrayList<>();
            Map<String, String> cks;

            cks = new HashMap<>();
            cks.put("namaMitra", "Aero Flyer Institute");
            cks.put("alamat", "Komp. Bandara Udara Budiarto Jl. PLP Curug, STPI Curug, Bitung Tangerang, Indonesia");
            cks.put("userNameKepalaOperasional", "KepalaAeroFlyerInstitute");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "Kanda University of International Studies");
            cks.put("alamat", "1-4-1 Wakaba, Mihama Ward, Chiba, Chiba Prefecture 261-0014, Japan");
            cks.put("userNameKepalaOperasional", "KepalaKandaUniv");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "Politeknik Energi dan Mineral Akamigas (PEM Akamigas)");
            cks.put("alamat", "Jalan Gajah Mada No. 38 Cepu, Blora, Jawa Tengah 58315");
            cks.put("userNameKepalaOperasional", "KepalaPEMAkamigas");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "Poltekkes Kemenkes Jakarta");
            cks.put("alamat", "Jl. Wijayakusuma Raya No. 47-48 Cilandak – Jakarta Selatan");
            cks.put("userNameKepalaOperasional", "KepalaPoltekkesJakarta");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "Poltekkes Kemenkes Semarang");
            cks.put("alamat", "Jl. Tirto Agung, Pedalangan, Kecamatan Banyumanik, Kota Semarang, Jawa Tengah 50268");
            cks.put("userNameKepalaOperasional", "KepalaPoltekkesSemarang");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "STIKES Akbidyo Yogyakarta");
            cks.put("alamat", "Jl. Parangtritis Km. 6, Sewon, Bantul, Yogyakarta");
            cks.put("userNameKepalaOperasional", "KepalaSTIKesAkbidyo");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "Universitas Airlangga");
            cks.put("alamat", "Kampus A UNAIR, Jl. Mayjen Prof. Dr. Moestopo No.47, Pacar Kembang, Kec. Tambaksari, Kota SBY, Jawa Timur 60132");
            cks.put("userNameKepalaOperasional", "KepalaUnair");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "Universitas Bhayangkara");
            cks.put("alamat", "Jl. Harsono No.67 Ragunan, Pasar Minggu Jakarta Selatan, 12550");
            cks.put("userNameKepalaOperasional", "KepalaUnivBhayangkara");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "Universitas Pancasila");
            cks.put("alamat", "Jl. Raya Lenteng Agung No.56-80, RT.1/RW.3, Srengseng Sawah, Jakarta, Kota Jakarta Selatan, Daerah Khusus Ibukota Jakarta 12640");
            cks.put("kontak", "+6281214585812");
            cks.put("userNameKepalaOperasional", "KepalaUnivPancasila");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "Universitas Trunojoyo");
            cks.put("alamat", "Gedung Cakra Lt. 5, Jl. Raya Telang PO BOX 2 Kamal - Bangkalan Madura 69162");
            cks.put("userNameKepalaOperasional", "KepalaUnivTrunojoyo");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "UPN Veteran");
            cks.put("alamat", "Jl. Padjajaran Condongcatur, Sleman, Daerah Istimewa Yogyakarta");
            cks.put("userNameKepalaOperasional", "KepalaUPNVeteran");
            cksList.add(cks);

            cks = new HashMap<>();
            cks.put("namaMitra", "Universitas Mercu Buana");
            cks.put("alamat", "Jl. Meruya Selatan No. 1 Kembangan Jakarta Barat 11650 (Kampus Meruya), Jl. Menteng Raya No. 29 Jakarta Pusat 10340 (Kampus Menteng), Jl. Warung Buncit No. 98 Jakarta Selatan 12750 (Kampus Warung Buncit)");
            cks.put("userNameKepalaOperasional", "KepalaMercuBuana");
            cksList.add(cks);

            for (int i = 0; i < cksList.size(); i++) {
                Map<String, String> cksMap = cksList.get(i);

                CabangKerjaSama cabangKerjaSama = new CabangKerjaSama();
                cabangKerjaSama.setNomorCabang(String.format("%03d", i + 2001));
                cabangKerjaSama.setNamaMitra(cksMap.get("namaMitra"));
                cabangKerjaSama.setAlamat(cksMap.get("alamat"));

                if (cksMap.containsKey("kontak")) {
                    cabangKerjaSama.setKontak(cksMap.get("kontak"));
                } else {
                    cabangKerjaSama.setKontak("021" + "-" + String.format("%03d", i + 2001) + "-" + String.format("%03d", i + 2001));
                }

                cabangKerjaSama.setJumlahKaryawan(20);
                cabangKerjaSama.setJamOperasional("08:00:00");
                cabangKerjaSama.setMasaBerlakuKontrak("2028-12-31");

                Pengguna kepalaOperasionalCabang = new Pengguna();
                kepalaOperasionalCabang.setIdKaryawan("12345" + String.format("%03d", i + 2001));
                kepalaOperasionalCabang.setUsername(cksMap.get("userNameKepalaOperasional"));
                kepalaOperasionalCabang.setNamaLengkap(faker.name().fullName());
                kepalaOperasionalCabang.setPassword(userRestService.hashPassword(cksMap.get("userNameKepalaOperasional")));
                kepalaOperasionalCabang.setRole("Kepala Operasional Cabang");
                kepalaOperasionalCabang.setEmail(faker.internet().emailAddress());
                kepalaOperasionalCabang.setNomorTelepon(faker.phoneNumber().cellPhone());
                kepalaOperasionalCabang.setNomorCabang(String.format("%03d", i + 2001));
                cabangKerjaSama.setKepalaOperasionalCabang(kepalaOperasionalCabang);

                System.out.println("User " + i + ": " + kepalaOperasionalCabang.getUsername() + " - Role: " + kepalaOperasionalCabang.getRole() + " - Cabang: " + kepalaOperasionalCabang.getNomorCabang() + " - Password: " + kepalaOperasionalCabang.getUsername());
                penggunaDb.save(kepalaOperasionalCabang);
                cabangKerjaSamaDb.save(cabangKerjaSama);

                CabangKerjaSama cksRevisi = cabangKerjaSamaDb.findByNomorCabang(String.format("%03d", i + 2001));
                Pengguna kepOpras = penggunaDb.findByIdKaryawan("12345" + String.format("%03d", i + 2001));
                List<Pengguna> daftarKaryawan = new ArrayList<>();
                daftarKaryawan.add(kepOpras);
                cksRevisi.setDaftarKaryawan(daftarKaryawan);
                cabangKerjaSamaDb.save(cksRevisi);
            }

            List<Barang> barangList = barangDb.findAll();

            for (int i = 0; i < 10; i++) {
                StokBarang stokBarang = new StokBarang();
                stokBarang.setNomorCabang("001");
                stokBarang.setKodeBarang(barangList.get(i).getKodeBarang());
                stokBarang.setKategoriBarang(barangList.get(i).getKategoriBarang());
                stokBarang.setNamaBarang(barangList.get(i).getNamaBarang());
                stokBarang.setHargaBarang(barangList.get(i).getHargaBarang());
                stokBarang.setBentuk(barangList.get(i).getBentuk());
                stokBarang.setStokBarang(100);
                stokBarangRepository.save(stokBarang);
                System.out.println("Stok Barang " + i + ": " + stokBarang.getKodeBarang() + " - Cabang: " + stokBarang.getNomorCabang() + " - Stok: " + stokBarang.getStokBarang());
            }


            System.out.println("Faker selesai");

        };
    }
}