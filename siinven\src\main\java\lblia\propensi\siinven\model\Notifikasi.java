package lblia.propensi.siinven.model;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "notifikasi")
public class Notifikasi {

    @Id
    @Column(name = "id_notifikasi", nullable = false, unique = true)
    private String idNotifikasi;
    
    @Column(nullable = false)
    private String rolePengirim;

    @Column(nullable = false)
    private String rolePenerima;

    @Column(nullable = true)
    private String nomorCabang;

    @Column(nullable = false)
    private String isiNotifikasi;

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "tanggal_notifikasi", nullable = false)
    private Date tanggalNotifikasi;

    @Column(nullable = false)
    private String idPengajuan;
}
