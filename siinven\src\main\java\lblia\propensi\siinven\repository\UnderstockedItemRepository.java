package lblia.propensi.siinven.repository;

import lblia.propensi.siinven.model.InputStokBarangCabang;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnderstockedItemRepository extends JpaRepository<InputStokBarangCabang, String> {
    
    @Query("SELECT i FROM InputStokBarangCabang i JOIN InputStokBarangCabangTotal t ON i.idPengajuan = t.idPengajuan WHERE t.step < 5")
    List<InputStokBarangCabang> findAllPendingRequests();
    
    @Query("SELECT i FROM InputStokBarangCabang i JOIN InputStokBarangCabangTotal t ON i.idPengajuan = t.idPengajuan WHERE t.step < 5 AND t.nomorCabangTujuan = :nomorCabang")
    List<InputStokBarangCabang> findPendingRequestsByCabang(@Param("nomorCabang") String nomorCabang);
    
    @Query("SELECT i FROM InputStokBarangCabang i JOIN InputStokBarangCabangTotal t ON i.idPengajuan = t.idPengajuan WHERE t.step < 5 AND i.namaBarang LIKE %:kategori%")
    List<InputStokBarangCabang> findPendingRequestsByKategori(@Param("kategori") String kategori);
    
    @Query("SELECT i FROM InputStokBarangCabang i JOIN InputStokBarangCabangTotal t ON i.idPengajuan = t.idPengajuan WHERE t.step < 5 AND t.nomorCabangTujuan = :nomorCabang AND i.namaBarang LIKE %:kategori%")
    List<InputStokBarangCabang> findPendingRequestsByCabangAndKategori(@Param("nomorCabang") String nomorCabang, @Param("kategori") String kategori);
    
    @Query("SELECT SUM(i.stokInput) FROM InputStokBarangCabang i JOIN InputStokBarangCabangTotal t ON i.idPengajuan = t.idPengajuan WHERE t.step < 5 AND i.kodeBarang = :kodeBarang")
    Integer getTotalRequestedStockByKodeBarang(@Param("kodeBarang") String kodeBarang);
}