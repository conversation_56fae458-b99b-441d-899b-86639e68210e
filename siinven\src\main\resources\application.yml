spring:
  main:
    allow-circular-references: true
  jpa:
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: create
  config:
    import: optional:file:.env[.properties]
  application:
    name: siinven
  datasource:
    url: ${DATABASE_URL_DEV}
    username: ${DEV_USERNAME}
    password: ${DEV_PASSWORD}

siinven:
  app:
    jwtSecret: "${JWT_SECRET_KEY}"
    jwtExpirationMs: 86400000

server:
  port: 8080
