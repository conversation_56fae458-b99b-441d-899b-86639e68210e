package lblia.propensi.siinven.dto.request;

import java.util.Date;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NotifikasiRequestDTO {

    @NotBlank(message = "Role Pengirim tidak boleh kosong")
    private String rolePengirim;

    @NotBlank(message = "Role Penerima tidak boleh kosong")
    private String rolePenerima;

    private String nomorCabang;

    @NotBlank(message = "Isi notifikasi tidak boleh kosong")
    private String isiNotifikasi;

    @NotNull(message = "Tanggal notifikasi tidak boleh kosong")
    private Date tanggalNotifikasi;

    private String idPengajuan;
}
