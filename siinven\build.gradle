plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.3'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'lblia.propensi'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
	maven { url 'https://repo.spring.io/snapshot' }
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'mysql:mysql-connector-java:8.0.32'
	implementation ('com.github.javafaker:javafaker:1.0.2') {
		exclude group: 'org.yaml', module: 'snakeyaml'
	}
	implementation('org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.5') {
		exclude group: 'org.springdoc', module: 'springdoc-openapi-webmvc-core'
	}
	implementation 'com.fasterxml.jackson.core:jackson-databind'
	implementation 'org.springframework.boot:spring-boot-starter-webflux'
	implementation 'jakarta.validation:jakarta.validation-api:3.0.0'
	implementation 'org.hibernate.validator:hibernate-validator:6.2.0.Final'
	implementation 'org.glassfish:javax.el:3.0.0'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'io.jsonwebtoken:jjwt-api:0.12.6'
	implementation 'jakarta.xml.bind:jakarta.xml.bind-api:4.0.2'
	implementation 'org.glassfish.jaxb:jaxb-runtime:4.0.5'
	implementation 'io.github.cdimascio:dotenv-java:3.0.0'

	runtimeOnly 'org.postgresql:postgresql'
	runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.6'
	runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.6'

	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	developmentOnly 'org.springframework.boot:spring-boot-devtools'

	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.junit.jupiter:junit-jupiter-api:5.7.0'
	testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.7.0'
	testImplementation 'org.mockito:mockito-core:3.9.0'
	testImplementation 'org.mockito:mockito-junit-jupiter:3.9.0'
	testImplementation platform("org.junit:junit-bom:5.11.2")
	testRuntimeOnly("org.junit.platform:junit-platform-launcher") {
		because("Only needed to run tests in a version of IntelliJ IDEA that bundles older versions")
	}
	testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
	testRuntimeOnly("org.junit.vintage:junit-vintage-engine")
}

tasks.named('test') {
	useJUnitPlatform()
}
