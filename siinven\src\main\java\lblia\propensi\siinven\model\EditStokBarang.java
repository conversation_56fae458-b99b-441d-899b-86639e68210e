package lblia.propensi.siinven.model;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "edit_stok_barang")
public class EditStokBarang {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "kode_barang", nullable = false)
    private Integer kodeBarang;

    @Column(name = "nomor_cabang", nullable = false)
    private String nomorCabang;

    @Column(name = "stok_sebelum", nullable = false)
    private Integer stokSebelum;

    @Column(name = "stok_sesudah", nullable = false)
    private Integer stokSesudah;

    @Column(name = "tanggal_edit", nullable = false)
    private Date tanggalEdit;

    @Column(name = "username_pengedit", nullable = false)
    private String usernamePengedit;

    @Column(name = "alasan_edit", nullable = true)
    private String alasanEdit;
}