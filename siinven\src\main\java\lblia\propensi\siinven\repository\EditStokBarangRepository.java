package lblia.propensi.siinven.repository;

import lblia.propensi.siinven.model.EditStokBarang;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EditStokBarangRepository extends JpaRepository<EditStokBarang, Long> {
    List<EditStokBarang> findByKodeBarangAndNomorCabang(Integer kodeBarang, String nomorCabang);
}