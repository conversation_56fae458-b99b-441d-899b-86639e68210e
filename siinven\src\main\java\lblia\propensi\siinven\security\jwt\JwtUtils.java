package lblia.propensi.siinven.security.jwt;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.repository.PenggunaDb;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import javax.crypto.SecretKey;
import java.util.Date;

@Component
public class JwtUtils {
    private static final Logger logger = LoggerFactory.getLogger(JwtUtils.class);

    @Autowired
    PenggunaDb penggunaDb;

    @Value("${siinven.app.jwtSecret}")
    private String jwtSecret;

    @Value("${siinven.app.jwtExpirationMs}")
    private int jwtExpirationMs;

    public String generateJwtToken(String username) {
        Pengguna pengguna = penggunaDb.findByUsername(username);

        return Jwts.builder()
                .subject(username)
                .claim("roles", pengguna.getRole())
                .claim("id", pengguna.getIdKaryawan())
                .claim("email", pengguna.getEmail())
                .issuedAt(new Date())
                .setExpiration(new Date((new Date()).getTime() + jwtExpirationMs))
                .signWith(Keys.hmacShaKeyFor(jwtSecret.getBytes()))
                .compact();
    }

    public String getUserNameFromJwtToken(String token) {
        JwtParser jwtParser = Jwts.parser().verifyWith(Keys.hmacShaKeyFor(jwtSecret.getBytes())).build();
        Claims claims = jwtParser.parse(token).accept(Jws.CLAIMS).getPayload();
        return claims.getSubject();
    }

    public String getRolesFromJWT(String authToken) {
        return Jwts.parser()
                .setSigningKey(Keys.hmacShaKeyFor(jwtSecret.getBytes()))
                .build()
                .parseClaimsJws(authToken)
                .getBody()
                .get("roles", String.class); // Extract roles as a list
    }

    public String decryptJWT(String token) {
        try {
            // Remove any whitespace or newline characters from the token
            String cleanToken = token.replaceAll("\\s", "");

            JwtParser jwtParser = Jwts.parser()
                    .setSigningKey(Keys.hmacShaKeyFor(jwtSecret.getBytes()))
                    .build();

            // Parse the token
            Claims claims = jwtParser.parseClaimsJws(cleanToken).getBody();

            // Log the decrypted claims
            logger.info("Decrypted JWT: {}", claims);

            return claims.toString(); // Return claims as a string representation
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired: {}", e.getMessage());
            throw new IllegalArgumentException("Token is expired");
        } catch (MalformedJwtException e) {
            logger.error("Malformed JWT token: {}", e.getMessage());
            throw new IllegalArgumentException("Malformed token");
        } catch (JwtException e) {
            logger.error("Error while decrypting JWT: {}", e.getMessage());
            throw new IllegalArgumentException("Invalid token");
        }
    }


    public boolean validateJwtToken(String authToken) {
        try {
            Jwts.parser().verifyWith(Keys.hmacShaKeyFor(jwtSecret.getBytes())).build().parse(authToken);
            return true;
        } catch (SignatureException e) {
            logger.error("Invalid JWT signature: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
        }
        return false;
    }
}

