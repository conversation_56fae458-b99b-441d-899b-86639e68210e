package lblia.propensi.siinven.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.hibernate.annotations.Where;
import org.hibernate.annotations.SQLDelete;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("deprecation")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="cabang_kerjasama")
@SQLDelete(sql = "UPDATE cabang_kerjasama SET deleted_at = CURRENT_TIMESTAMP WHERE nomor_cabang=?")
@Where(clause = "deleted_at IS NULL")
public class CabangKerjaSama {
    @Id
    @Column(name = "nomor_cabang", nullable = false, updatable = false)
    private String nomorCabang;

    @Column(name = "nama_mitra", updatable = false, nullable = true, unique = true)
    private String namaMitra;

    @Column(name = "alamat", nullable = true)
    private String alamat;

    @Column(name = "kontak", nullable = true)
    private String kontak;

    @Column(name = "jumlah_karyawan", nullable = true)
    private int jumlahKaryawan;
    
    @Column(name = "jam_operasional", nullable = true)
    private String jamOperasional;

    @Column(name = "masa_berlaku_kontrak", nullable = true)
    private String masaBerlakuKontrak;

    @OneToOne(fetch = FetchType.EAGER, cascade = CascadeType.DETACH)
    @JoinColumn(name = "id_kepala_operasional_cabang", referencedColumnName = "idKaryawan")
    private Pengguna kepalaOperasionalCabang;

    @OneToMany(mappedBy = "cabangKerjaSama", fetch = FetchType.EAGER, cascade = CascadeType.DETACH)
    private List<Pengguna> daftarKaryawan = new ArrayList<>(); 
    
    @Column(name = "is_cabang_asli", nullable = false, updatable = false)
    private Boolean isCabangAsli = false;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", columnDefinition = "TIMESTAMP", nullable = false)
    private Date createdAt = new Date();

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", columnDefinition = "TIMESTAMP", nullable = false)
    private Date updatedAt;

    @Column(name = "deleted_at", columnDefinition = "TIMESTAMP", nullable = true)
    private Date deletedAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = new Date();
    }
}
