package lblia.propensi.siinven.service;

import jakarta.transaction.Transactional;
import lblia.propensi.siinven.controller.authentication.AuthenticationController;
import lblia.propensi.siinven.dto.request.authentication.GantiPasswordDTO;
import lblia.propensi.siinven.dto.request.authentication.PenggunaEditDTO;
import lblia.propensi.siinven.dto.request.authentication.PenggunaLoginDTO;
import lblia.propensi.siinven.dto.request.authentication.PenggunaRegisterDTO;
import lblia.propensi.siinven.dto.response.NotifikasiResponseDTO;
import lblia.propensi.siinven.dto.response.authentication.PenggunaUsernameResponse;
import lblia.propensi.siinven.dto.response.authentication.ProfilePenggunaResponseDTO;
import lblia.propensi.siinven.model.CabangAsli;
import lblia.propensi.siinven.model.CabangKerjaSama;
import lblia.propensi.siinven.model.Notifikasi;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.repository.CabangAsliDb;
import lblia.propensi.siinven.repository.CabangKerjaSamaDb;
import lblia.propensi.siinven.repository.NotifikasiDb;
import lblia.propensi.siinven.repository.PenggunaDb;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Service
@Transactional
public class UserRestServiceImpl implements UserRestService{

    private static final Logger logger = LoggerFactory.getLogger(UserRestServiceImpl.class);
    private final PenggunaDb penggunaDb;
    private final NotifikasiDb notifikasiDb;
    private final CabangAsliDb cabangAsliDb;
    private final CabangKerjaSamaDb cabangKerjaSamaDb;

    public UserRestServiceImpl(PenggunaDb penggunaDb, NotifikasiDb notifikasiDb, CabangAsliDb cabangAsliDb, CabangKerjaSamaDb cabangKerjaSamaDb) {
        this.penggunaDb = penggunaDb;
        this.notifikasiDb = notifikasiDb;
        this.cabangAsliDb = cabangAsliDb;
        this.cabangKerjaSamaDb = cabangKerjaSamaDb;
    }

    @Override
    public String hashPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    @Override
    public ProfilePenggunaResponseDTO login(PenggunaLoginDTO user) {
        try {
            Pengguna pengguna = penggunaDb.findByUsername(user.getUsername());
            if (pengguna != null) {
                BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
                if (passwordEncoder.matches(user.getPassword(), pengguna.getPassword())) {
                    return convertToResponseDTO(pengguna);
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public Pengguna getUserByUsername(String username) {
        return penggunaDb.findByUsername(username);
    }

    @Override
    public String createUser(PenggunaRegisterDTO user) {
        try {
            Pengguna newUser = new Pengguna();
            newUser.setIdKaryawan(user.getIdKaryawan());
            newUser.setUsername(user.getUsername());
            newUser.setNamaLengkap(user.getNamaLengkap());
            newUser.setPassword(hashPassword(user.getPassword()));
            newUser.setRole(user.getRole());
            newUser.setEmail(user.getEmail() != null ? user.getEmail() : "");
            newUser.setNomorTelepon(user.getNomorTelepon() != null ? user.getNomorTelepon() : "");
            newUser.setNomorCabang("001");
            newUser.setIsDeleted(null);

            penggunaDb.save(newUser);
            return "User successfully created!";
        } catch (Exception e) {
            e.printStackTrace(); // Logs the error (for debugging)
            return "Failed to create user: " + e.getMessage();
        }
    }

    @Override
    public ProfilePenggunaResponseDTO getProfile(String username) {
        try {
            Pengguna pengguna = penggunaDb.findByUsername(username);
            if (pengguna != null) {
                return convertToResponseDTO(pengguna);
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public ProfilePenggunaResponseDTO updateUser(PenggunaEditDTO profile) {
        try {
            Pengguna pengguna = penggunaDb.findByUsername(profile.getUsername());
            if (pengguna != null) {
                if(profile.getEmail() != null) {
                    pengguna.setEmail(profile.getEmail());
                }
                if(pengguna.getNomorCabang() != null) {
                    pengguna.setNomorCabang(profile.getNomorCabang());
                }
                if(profile.getRole() != null) {
                    pengguna.setRole(profile.getRole());
                }
                if(profile.getNomorTelepon() != null) {
                    pengguna.setNomorTelepon(profile.getNomorTelepon());
                }
                penggunaDb.save(pengguna);
                return convertToResponseDTO(pengguna);
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private ProfilePenggunaResponseDTO convertToResponseDTO(Pengguna pengguna) {
        try {
            ProfilePenggunaResponseDTO responseDTO = new ProfilePenggunaResponseDTO();
            responseDTO.setIdKaryawan(pengguna.getIdKaryawan());
            responseDTO.setUsername(pengguna.getUsername());
            responseDTO.setNamaLengkap(pengguna.getNamaLengkap());
            responseDTO.setEmail(pengguna.getEmail());
            responseDTO.setRole(pengguna.getRole());
            responseDTO.setNomorTelepon(pengguna.getNomorTelepon());
            responseDTO.setNomorCabang(pengguna.getNomorCabang());
            responseDTO.setCabangAsli(getCabangAsli(pengguna.getNomorCabang()));
            return responseDTO;
        } catch (Exception e) {
            e.printStackTrace(); // Logs
        }

        return null;
    }

    private boolean getCabangAsli(String nomorCabang) {
        if(nomorCabang.equals("001")) {
            return true;
        }

        CabangAsli cabangASli = cabangAsliDb.findById(nomorCabang).orElse(null);

        if(cabangASli != null) {
            return true;
        } else {
            CabangKerjaSama cabangKerjaSama = cabangKerjaSamaDb.findById(nomorCabang).orElse(null);
            if(cabangKerjaSama != null) {
                return false;
            } else {
                return false;
            }
        }


    }

    @Override
    public Pengguna getUserByIdPengguna(String id) {
        return penggunaDb.findByIdKaryawan(id);
    }

    @Override
    public List<ProfilePenggunaResponseDTO> getAllUser() {
        List<ProfilePenggunaResponseDTO> responseList = new ArrayList<>();
        for(Pengguna pengguna : penggunaDb.findAll()) {
            ProfilePenggunaResponseDTO responseDTO = convertToResponseDTO(pengguna);
            if (responseDTO != null) {
                responseList.add(responseDTO);
            }
        }
        return responseList;
    }

    @Override
    public ProfilePenggunaResponseDTO changePassword(String username, GantiPasswordDTO gantiPasswordDTO) {
        try {
            Pengguna pengguna = penggunaDb.findByUsername(username);
            String oldPassword = gantiPasswordDTO.getOldPassword().trim();
            String password = gantiPasswordDTO.getNewPassword().trim();
            BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
            if(!passwordEncoder.matches(oldPassword, pengguna.getPassword())) {
                return null;
            }
            logger.info("New Password: " + password);

            if (gantiPasswordDTO.getOldPassword() == null || gantiPasswordDTO.getNewPassword() == null) {
                return null;
            }
            logger.info("Username: " + username);

            if (pengguna != null && password != null) {
                pengguna.setPassword(hashPassword(password));
                penggunaDb.save(pengguna);
                return convertToResponseDTO(pengguna);
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public List<NotifikasiResponseDTO> getNotifikasiByRoleDanCabang(String rolePenerima, String nomorCabang) {
        try {
            List<Notifikasi> notifikasiList = notifikasiDb.findAll();
            List<NotifikasiResponseDTO> result = new ArrayList<>();
            if (!rolePenerima.equals("Kepala Operasional Cabang")) {
                for(Notifikasi notifikasi : notifikasiList) {
                    if (notifikasi.getRolePenerima().equals(rolePenerima)) {
                        NotifikasiResponseDTO responseDTO = new NotifikasiResponseDTO();
                        responseDTO.setIdNotifikasi(notifikasi.getIdNotifikasi());
                        responseDTO.setRolePenerima(notifikasi.getRolePenerima());
                        responseDTO.setNomorCabang(notifikasi.getNomorCabang());
                        responseDTO.setIsiNotifikasi(notifikasi.getIsiNotifikasi());
                        responseDTO.setIdPengajuan(notifikasi.getIdPengajuan());
                        responseDTO.setRolePengirim(notifikasi.getRolePengirim());
                        responseDTO.setTanggalNotifikasi(notifikasi.getTanggalNotifikasi());
                        result.add(responseDTO);
                    }
                }
                return result;
            } else {
                for(Notifikasi notifikasi: notifikasiList) {
                    if(notifikasi.getNomorCabang() != null) {
                        if (notifikasi.getNomorCabang().equals(nomorCabang) && notifikasi.getRolePenerima().equals("Kepala Operasional Cabang")) {
                            NotifikasiResponseDTO responseDTO = new NotifikasiResponseDTO();
                            responseDTO.setIdNotifikasi(notifikasi.getIdNotifikasi());
                            responseDTO.setRolePenerima(notifikasi.getRolePenerima());
                            responseDTO.setNomorCabang(notifikasi.getNomorCabang());
                            responseDTO.setIsiNotifikasi(notifikasi.getIsiNotifikasi());
                            responseDTO.setIdPengajuan(notifikasi.getIdPengajuan());
                            responseDTO.setRolePengirim(notifikasi.getRolePengirim());
                            responseDTO.setTanggalNotifikasi(notifikasi.getTanggalNotifikasi());
                            result.add(responseDTO);
                        }
                    }
                }
                result.sort(Comparator.comparing(NotifikasiResponseDTO::getTanggalNotifikasi).reversed());
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
