package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.response.UnderstockedItemResponseDTO;
import lblia.propensi.siinven.model.Barang;
import lblia.propensi.siinven.model.InputStokBarangCabang;
import lblia.propensi.siinven.model.InputStokBarangCabangTotal;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.repository.BarangDb;
import lblia.propensi.siinven.repository.CabangAsliDb;
import lblia.propensi.siinven.repository.CabangKerjaSamaDb;
import lblia.propensi.siinven.repository.StokBarangRepository;
import lblia.propensi.siinven.repository.UnderstockedItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.stream.Collectors;

@Service
public class UnderstockedItemServiceImpl implements UnderstockedItemService {

    private final UnderstockedItemRepository understockedItemRepository;
    private final StokBarangRepository stokBarangRepository;
    private final CabangAsliDb cabangAsliDb;
    private final CabangKerjaSamaDb cabangKerjaSamaDb;
    private final BarangDb barangDb;

    @Autowired
    public UnderstockedItemServiceImpl(
            UnderstockedItemRepository understockedItemRepository,
            StokBarangRepository stokBarangRepository,
            CabangAsliDb cabangAsliDb,
            CabangKerjaSamaDb cabangKerjaSamaDb,
            BarangDb barangDb) {
        this.understockedItemRepository = understockedItemRepository;
        this.stokBarangRepository = stokBarangRepository;
        this.cabangAsliDb = cabangAsliDb;
        this.cabangKerjaSamaDb = cabangKerjaSamaDb;
        this.barangDb = barangDb;
    }

    @Override
    public List<UnderstockedItemResponseDTO> getAllUnderstockedItems() {
        List<InputStokBarangCabang> pendingRequests = understockedItemRepository.findAllPendingRequests();
        return processRequests(pendingRequests);
    }

    @Override
    public List<UnderstockedItemResponseDTO> getUnderstockedItemsByCabang(String nomorCabang) {
        List<InputStokBarangCabang> pendingRequests = understockedItemRepository.findPendingRequestsByCabang(nomorCabang);
        return processRequests(pendingRequests);
    }

    @Override
    public List<UnderstockedItemResponseDTO> getUnderstockedItemsByKategori(String kategori) {
        List<InputStokBarangCabang> pendingRequests = understockedItemRepository.findPendingRequestsByKategori(kategori);
        return processRequests(pendingRequests);
    }

    @Override
    public List<UnderstockedItemResponseDTO> getUnderstockedItemsByCabangAndKategori(String nomorCabang, String kategori) {
        List<InputStokBarangCabang> pendingRequests = understockedItemRepository.findPendingRequestsByCabangAndKategori(nomorCabang, kategori);
        return processRequests(pendingRequests);
    }

    private List<UnderstockedItemResponseDTO> processRequests(List<InputStokBarangCabang> requests) {
        // Group requests by item code
        Map<String, List<InputStokBarangCabang>> requestsByItem = requests.stream()
                .collect(Collectors.groupingBy(InputStokBarangCabang::getKodeBarang));

        List<UnderstockedItemResponseDTO> result = new ArrayList<>();

        // Process each item
        for (Map.Entry<String, List<InputStokBarangCabang>> entry : requestsByItem.entrySet()) {
            String kodeBarang = entry.getKey();
            List<InputStokBarangCabang> itemRequests = entry.getValue();

            // Get total requested stock for this item
            int totalRequestedStock = itemRequests.stream()
                    .mapToInt(InputStokBarangCabang::getStokInput)
                    .sum();

            // Get current stock at headquarters
            int currentStock = 0;
            String namaBarang = "";
            String kategoriBarang = "";

            // Find the item in StokBarang (headquarters stock)
            List<StokBarang> stokBarangList = stokBarangRepository.findAll();
            for (StokBarang stokBarang : stokBarangList) {
                if (stokBarang.getKodeBarang().toString().equals(kodeBarang) &&
                    (stokBarang.getNomorCabang() == null || stokBarang.getNomorCabang().equals("001"))) {
                    currentStock = stokBarang.getStokBarang();
                    namaBarang = stokBarang.getNamaBarang();
                    kategoriBarang = stokBarang.getKategoriBarang();
                    break;
                }
            }

            // Determine status
            String status = (currentStock >= totalRequestedStock) ? "valid" : "understocked";

            // Create response DTOs for each branch request
            for (InputStokBarangCabang request : itemRequests) {
                String cabangId = "";
                try {
                    // Get the parent InputStokBarangCabangTotal to find the branch
                    Optional<InputStokBarangCabangTotal> totalOpt = understockedItemRepository.findById(request.getIdPengajuan())
                            .map(r -> {
                                InputStokBarangCabangTotal total = new InputStokBarangCabangTotal();
                                total.setIdPengajuan(r.getIdPengajuan());
                                return total;
                            });

                    if (totalOpt.isPresent()) {
                        cabangId = totalOpt.get().getNomorCabangTujuan();
                    }
                } catch (Exception e) {
                    // If we can't get the branch ID, use a placeholder
                    cabangId = "Unknown";
                }

                // Get branch name
                String namaCabang = "Unknown";
                if (cabangAsliDb.findById(cabangId).isPresent()) {
                    namaCabang = cabangAsliDb.findById(cabangId).get().getNamaCabang();
                } else if (cabangKerjaSamaDb.findByNomorCabang(cabangId) != null) {
                    namaCabang = cabangKerjaSamaDb.findByNomorCabang(cabangId).getNamaMitra();
                }

                UnderstockedItemResponseDTO responseDTO = new UnderstockedItemResponseDTO();
                responseDTO.setKodeBarang(kodeBarang);
                responseDTO.setNamaBarang(namaBarang);
                responseDTO.setKategoriBarang(kategoriBarang);
                responseDTO.setStokDiminta(request.getStokInput());
                responseDTO.setStokPusat(currentStock);
                responseDTO.setNamaCabang(namaCabang);
                responseDTO.setStatus(status);

                result.add(responseDTO);
            }
        }

        return result;
    }

    @Override
    public List<String> getAllKategori() {
        return barangDb.findAllActiveBranches().stream()
                .map(Barang::getKategoriBarang)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, String>> getAllCabang() {
        List<Map<String, String>> result = new ArrayList<>();

        // Get all Cabang Asli
        cabangAsliDb.findAllActiveBranches().forEach(cabang -> {
            Map<String, String> cabangMap = new HashMap<>();
            cabangMap.put("nomorCabang", cabang.getNomorCabang());
            cabangMap.put("namaCabang", cabang.getNamaCabang());
            cabangMap.put("type", "CA"); // Cabang Asli
            result.add(cabangMap);
        });

        // Get all Cabang Kerja Sama
        cabangKerjaSamaDb.findAll().forEach(cabang -> {
            Map<String, String> cabangMap = new HashMap<>();
            cabangMap.put("nomorCabang", cabang.getNomorCabang());
            cabangMap.put("namaCabang", cabang.getNamaMitra());
            cabangMap.put("type", "CKS"); // Cabang Kerja Sama
            result.add(cabangMap);
        });

        return result;
    }
}
