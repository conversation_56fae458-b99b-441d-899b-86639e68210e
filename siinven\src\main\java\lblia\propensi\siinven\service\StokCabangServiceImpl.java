package lblia.propensi.siinven.service;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.model.StokCabang;
import lblia.propensi.siinven.repository.StokCabangRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class StokCabangServiceImpl implements StokCabangService {

    @Autowired
    private StokCabangRepository stokCabangRepository;

    @Override
    public BaseResponseDTO<List<StokCabang>> getAllStokCabang() {
        var response = new BaseResponseDTO<List<StokCabang>>();
        List<StokCabang> stokCabangList = stokCabangRepository.findAll();

        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Data stok cabang berhasil diambil.");
        response.setData(stokCabangList);
        response.setTimestamp(new Date());

        return response;
    }

    @Override
    public BaseResponseDTO<StokCabang> addStokCabang(StokCabang stokCabang) {
        var response = new BaseResponseDTO<StokCabang>();
    
        // Change this validation to check other required fields instead of kodeBarang
        if (Objects.isNull(stokCabang) || 
            Objects.isNull(stokCabang.getNamaBarang()) || 
            Objects.isNull(stokCabang.getKategoriBarang()) ||
            Objects.isNull(stokCabang.getHargaBarang()) ||
            Objects.isNull(stokCabang.getBentuk()) ||
            Objects.isNull(stokCabang.getStokBarang())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, 
                "Data stok cabang tidak valid. Pastikan semua data telah diisi dengan benar.");
        }
    
        // Only check if the ID exists if it's not null (for updates)
        if (stokCabang.getKodeBarang() != null && stokCabangRepository.existsById(stokCabang.getKodeBarang())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, 
                "Data dengan kode barang " + stokCabang.getKodeBarang() + " sudah ada.");
        }
    
        StokCabang savedData = stokCabangRepository.save(stokCabang);
    
        response.setStatus(HttpStatus.CREATED.value());
        response.setMessage("Data stok cabang berhasil ditambahkan.");
        response.setData(savedData);
        response.setTimestamp(new Date());
    
        return response;
    }

    @Override
    public StokCabang getCabangData() {
        return stokCabangRepository.findAll().stream().findFirst()
                .orElseThrow(() -> new RuntimeException("Data stok cabang tidak ditemukan."));
    }

    @Override
    public void saveStokCabangData(StokCabang stokCabang) {
        stokCabangRepository.save(stokCabang);
    }
}

