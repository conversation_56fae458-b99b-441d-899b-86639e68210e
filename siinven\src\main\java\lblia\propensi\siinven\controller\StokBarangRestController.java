package lblia.propensi.siinven.controller;

import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.StokBarangResponseDTO;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.service.StokBarangService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/home/<USER>")
public class StokBarangRestController {

    @Autowired
    private StokBarangService stokBarangService;

    @GetMapping("/stok")
    public ResponseEntity<BaseResponseDTO<List<StokBarangResponseDTO>>> getAllStokBarang() {
        List<StokBarangResponseDTO> stokBarangList = stokBarangService.getAllStokBarang();

        if (stokBarangList.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.NOT_FOUND.value(),
                            "Data stok barang tidak ditemukan",
                            new Date(),
                            null
                    ));
        }

        return ResponseEntity.ok(
                new BaseResponseDTO<>(
                        HttpStatus.OK.value(),
                        "Data stok barang berhasil ditemukan",
                        new Date(),
                        stokBarangList
                )
        );
    }

    @GetMapping("/stok/{kodeBarang}")
    public ResponseEntity<BaseResponseDTO<StokBarangResponseDTO>> getStokBarangByKode(
            @PathVariable Integer kodeBarang) {
        try {
            StokBarangResponseDTO stokBarang = stokBarangService.getStokBarangByKode(kodeBarang);
            
            if (stokBarang == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new BaseResponseDTO<>(
                                HttpStatus.NOT_FOUND.value(),
                                "Data stok barang dengan kode " + kodeBarang + " tidak ditemukan",
                                new Date(),
                                null
                        ));
            }
            
            return ResponseEntity.ok(
                    new BaseResponseDTO<>(
                            HttpStatus.OK.value(),
                            "Data stok barang berhasil ditemukan",
                            new Date(),
                            stokBarang
                    )
            );
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            "Terjadi kesalahan saat mengambil data: " + e.getMessage(),
                            new Date(),
                            null
                    ));
        }
    }

    @GetMapping("/cabang/{nomorCabang}")
    public ResponseEntity<BaseResponseDTO<List<StokBarangResponseDTO>>> getStokBarangByNomorCabang(
            @PathVariable String nomorCabang) {

        List<StokBarangResponseDTO> stokBarangList = stokBarangService.getStokBarangByNomorCabang(nomorCabang);

        if (stokBarangList.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.NOT_FOUND.value(),
                            "Data tidak ditemukan untuk nomor cabang " + nomorCabang,
                            new Date(),
                            null
                    ));
        }

        return ResponseEntity.ok(
                new BaseResponseDTO<>(
                        HttpStatus.OK.value(),
                        "Data ditemukan",
                        new Date(),
                        stokBarangList
                )
        );
    }

    @PostMapping("/stok")
    public ResponseEntity<BaseResponseDTO<StokBarangResponseDTO>> addStokBarang(@RequestBody StokBarang stokBarang) {
        try {
            StokBarangResponseDTO savedStokBarang = stokBarangService.addStokBarang(stokBarang);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.CREATED.value(),
                            "Data berhasil ditambahkan",
                            new Date(),
                            savedStokBarang
                    ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            "Terjadi kesalahan saat menambahkan data: " + e.getMessage(),
                            new Date(),
                            null
                    ));
        }
    }

    @PutMapping("/stok/{kodeBarang}")
    public ResponseEntity<BaseResponseDTO<StokBarangResponseDTO>> updateStokBarang(
            @PathVariable Integer kodeBarang,
            @RequestBody StokBarang stokBarang) {
        try {
            stokBarang.setKodeBarang(kodeBarang);
            StokBarangResponseDTO updatedStokBarang = stokBarangService.updateStokBarang(stokBarang);
            
            if (updatedStokBarang == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new BaseResponseDTO<>(
                                HttpStatus.NOT_FOUND.value(),
                                "Data stok barang dengan kode " + kodeBarang + " tidak ditemukan",
                                new Date(),
                                null
                        ));
            }
            
            return ResponseEntity.ok(
                    new BaseResponseDTO<>(
                            HttpStatus.OK.value(),
                            "Data berhasil diperbarui",
                            new Date(),
                            updatedStokBarang
                    )
            );
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            "Terjadi kesalahan saat memperbarui data: " + e.getMessage(),
                            new Date(),
                            null
                    ));
        }
    }

    @DeleteMapping("/stok/{kodeBarang}")
    public ResponseEntity<BaseResponseDTO<String>> deleteStokBarang(@PathVariable Integer kodeBarang) {
        try {
            boolean deleted = stokBarangService.deleteStokBarang(kodeBarang);
            
            if (!deleted) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new BaseResponseDTO<>(
                                HttpStatus.NOT_FOUND.value(),
                                "Data stok barang dengan kode " + kodeBarang + " tidak ditemukan",
                                new Date(),
                                null
                        ));
            }
            
            return ResponseEntity.ok(
                    new BaseResponseDTO<>(
                            HttpStatus.OK.value(),
                            "Data berhasil dihapus",
                            new Date(),
                            "Stok barang dengan kode " + kodeBarang + " telah dihapus"
                    )
            );
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            "Terjadi kesalahan saat menghapus data: " + e.getMessage(),
                            new Date(),
                            null
                    ));
        }
    }

    @GetMapping("/kategori/{kategoriBarang}")
    public ResponseEntity<BaseResponseDTO<List<StokBarangResponseDTO>>> getStokBarangByKategori(
            @PathVariable String kategoriBarang) {
        try {
            List<StokBarangResponseDTO> stokBarangList = stokBarangService.getStokBarangByKategori(kategoriBarang);
            
            if (stokBarangList.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new BaseResponseDTO<>(
                                HttpStatus.NOT_FOUND.value(),
                                "Data stok barang dengan kategori " + kategoriBarang + " tidak ditemukan",
                                new Date(),
                                null
                        ));
            }
            
            return ResponseEntity.ok(
                    new BaseResponseDTO<>(
                            HttpStatus.OK.value(),
                            "Data ditemukan",
                            new Date(),
                            stokBarangList
                    )
            );
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            "Terjadi kesalahan saat mengambil data: " + e.getMessage(),
                            new Date(),
                            null
                    ));
        }
    }
    @GetMapping("/cabang/nonpusat")
    public ResponseEntity<BaseResponseDTO<List<StokBarangResponseDTO>>> getAllStokBarangExceptMainBranch() {
        try {
            List<StokBarangResponseDTO> stokBarangList = stokBarangService.getAllStokBarangExceptMainBranch();
            
            if (stokBarangList.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new BaseResponseDTO<>(
                                HttpStatus.NOT_FOUND.value(),
                                "Data stok barang dari cabang selain pusat tidak ditemukan",
                                new Date(),
                                null
                        ));
            }
            
            return ResponseEntity.ok(
                    new BaseResponseDTO<>(
                            HttpStatus.OK.value(),
                            "Data stok barang dari cabang selain pusat berhasil ditemukan",
                            new Date(),
                            stokBarangList
                    )
            );
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            "Terjadi kesalahan saat mengambil data: " + e.getMessage(),
                            new Date(),
                            null
                    ));
        }
    }

   
    
    @PutMapping("/stok/{kodeBarang}/{nomorCabang}")
    public ResponseEntity<BaseResponseDTO<StokBarangResponseDTO>> updateStokBarangWithCabang(
            @PathVariable Integer kodeBarang,
            @PathVariable String nomorCabang,
            @RequestBody StokBarang stokBarang) {
        try {
            // Set the kodeBarang and nomorCabang from path variables
            stokBarang.setKodeBarang(kodeBarang);
            stokBarang.setNomorCabang(nomorCabang);
            
            // First check if the record exists
            boolean exists = stokBarangService.existsByKodeBarangAndNomorCabang(kodeBarang, nomorCabang);
            
            if (!exists) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new BaseResponseDTO<>(
                                HttpStatus.NOT_FOUND.value(),
                                "Data stok barang dengan kode " + kodeBarang + " dan nomor cabang " + nomorCabang + " tidak ditemukan",
                                new Date(),
                                null
                        ));
            }
            
            // Update the existing record
            StokBarangResponseDTO updatedStokBarang = stokBarangService.updateStokBarang(stokBarang);
            
            return ResponseEntity.ok(
                    new BaseResponseDTO<>(
                            HttpStatus.OK.value(),
                            "Data berhasil diperbarui",
                            new Date(),
                            updatedStokBarang
                    )
            );
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            "Terjadi kesalahan saat memperbarui data: " + e.getMessage(),
                            new Date(),
                            null
                    ));
        }
    }
    
    
    @DeleteMapping("/stok/{kodeBarang}/{nomorCabang}")
    public ResponseEntity<BaseResponseDTO<String>> deleteStokBarangByKodeAndNomorCabang(
            @PathVariable Integer kodeBarang,
            @PathVariable String nomorCabang) {
        try {
            boolean deleted = stokBarangService.deleteStokBarangByKodeAndNomorCabang(kodeBarang, nomorCabang);
            
            if (!deleted) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new BaseResponseDTO<>(
                                HttpStatus.NOT_FOUND.value(),
                                "Data stok barang dengan kode " + kodeBarang + " dan nomor cabang " + nomorCabang + " tidak ditemukan",
                                new Date(),
                                null
                        ));
            }
            
            return ResponseEntity.ok(
                    new BaseResponseDTO<>(
                            HttpStatus.OK.value(),
                            "Data berhasil dihapus",
                            new Date(),
                            "Stok barang dengan kode " + kodeBarang + " dan nomor cabang " + nomorCabang + " telah dihapus"
                    )
            );
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            "Terjadi kesalahan saat menghapus data: " + e.getMessage(),
                            new Date(),
                            null
                    ));
        }
    }
    



@GetMapping("/stok/{kodeBarang}/{nomorCabang}")
public ResponseEntity<BaseResponseDTO<StokBarangResponseDTO>> getStokBarangByKodeAndNomorCabang(
        @PathVariable Integer kodeBarang,
        @PathVariable String nomorCabang) {
    try {
        StokBarangResponseDTO stokBarang = stokBarangService.getStokBarangByKodeAndNomorCabang(kodeBarang, nomorCabang);
        
        if (stokBarang == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new BaseResponseDTO<>(
                            HttpStatus.NOT_FOUND.value(),
                            "Data stok barang dengan kode " + kodeBarang + " dan nomor cabang " + nomorCabang + " tidak ditemukan",
                            new Date(),
                            null
                    ));
        }
        
        return ResponseEntity.ok(
                new BaseResponseDTO<>(
                        HttpStatus.OK.value(),
                        "Data stok barang berhasil ditemukan",
                        new Date(),
                        stokBarang
                )
        );
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new BaseResponseDTO<>(
                        HttpStatus.INTERNAL_SERVER_ERROR.value(),
                        "Terjadi kesalahan saat mengambil data: " + e.getMessage(),
                        new Date(),
                        null
                ));
    }
}
}