package lblia.propensi.siinven.dto.response.cabang_asli;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PengadaanCabangAsliTotalResponse {
    private String idPengajuan;
    private String nomorCabang;
    private List<PengadaanCabangAsliResponse> listInputStokBarang;
    private String totalHarga;
    private int step;
    private boolean flag;
}
