package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.ApprovalRequestDTO;
import lblia.propensi.siinven.dto.request.KonfirmasiReturnRequestDTO;
import lblia.propensi.siinven.dto.request.ReturnRequestDTO;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.ReturnResponseDTO;

import java.util.List;

public interface ReturnService {
    
    BaseResponseDTO<ReturnResponseDTO> createReturn(ReturnRequestDTO returnRequestDTO, String idPengaju);
    
    BaseResponseDTO<List<ReturnResponseDTO>> getAllReturns();
    
    BaseResponseDTO<List<ReturnResponseDTO>> getReturnsByStatus(String status);
    
    BaseResponseDTO<ReturnResponseDTO> getReturnById(String idReturn);
    
    BaseResponseDTO<ReturnResponseDTO> approveReturn(String idReturn, ApprovalRequestDTO approvalDTO, String idApprover);
    
    BaseResponseDTO<ReturnResponseDTO> updateStatusRetur(String idReturn, String newStatus);
    
    BaseResponseDTO<ReturnResponseDTO> konfirmasiReturn(String idReturn, KonfirmasiReturnRequestDTO konfirmasiDTO);
}