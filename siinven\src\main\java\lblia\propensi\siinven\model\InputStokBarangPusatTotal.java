package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="input_stok_barang_pusat_total")
public class InputStokBarangPusatTotal {
    @Id
    private String idPengajuan;

    @Column(name = "totalHarga", nullable = true)
    private Double totalHarga;

    @Column(name = "step", nullable = true)
    private int step;

    @Column(name="waktu_pengajuan", nullable = true)
    private LocalDateTime waktuPengajuan;
}
