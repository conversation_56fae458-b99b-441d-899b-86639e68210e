package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.EditStokBarangRequest;
import lblia.propensi.siinven.dto.response.EditStokBarangResponse;
import lblia.propensi.siinven.dto.response.StokBarangDetailDTO;
import lblia.propensi.siinven.model.EditStokBarang;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.repository.EditStokBarangRepository;
import lblia.propensi.siinven.repository.StokBarangRepository;
import lblia.propensi.siinven.service.EditStokBarangService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class EditStokBarangServiceImpl implements EditStokBarangService {

    @Autowired
    private StokBarangRepository stokBarangRepository;

    @Autowired
    private EditStokBarangRepository editStokBarangRepository;

    @Override
    public EditStokBarangResponse editStokBarang(EditStokBarangRequest request, String username) {
        try {
            StokBarang stokBarang = getStokBarangByKodeAndCabang(request.getKodeBarang(), request.getNomorCabang());
            
            if (stokBarang == null) {
                return new EditStokBarangResponse(
                    request.getKodeBarang(),
                    null,
                    request.getNomorCabang(),
                    0,
                    request.getStokBaru(),
                    "Barang tidak ditemukan di cabang tersebut",
                    false
                );
            }
            
            int stokSebelum = stokBarang.getStokBarang();
            
            // Record the edit in history
            EditStokBarang editRecord = new EditStokBarang();
            editRecord.setKodeBarang(request.getKodeBarang());
            editRecord.setNomorCabang(request.getNomorCabang());
            editRecord.setStokSebelum(stokSebelum);
            editRecord.setStokSesudah(request.getStokBaru());
            editRecord.setTanggalEdit(new Date());
            editRecord.setUsernamePengedit(username);
            editRecord.setAlasanEdit(request.getAlasanEdit());
            editStokBarangRepository.save(editRecord);
            
            // Update the actual stock
            stokBarang.setStokBarang(request.getStokBaru());
            stokBarangRepository.save(stokBarang);
            
            return new EditStokBarangResponse(
                stokBarang.getKodeBarang(),
                stokBarang.getNamaBarang(),
                stokBarang.getNomorCabang(),
                stokSebelum,
                request.getStokBaru(),
                "Stok berhasil diperbarui",
                true
            );
            
        } catch (Exception e) {
            return new EditStokBarangResponse(
                request.getKodeBarang(),
                null,
                request.getNomorCabang(),
                0,
                request.getStokBaru(),
                "Terjadi kesalahan: " + e.getMessage(),
                false
            );
        }
    }

    @Override
    public List<StokBarangDetailDTO> getStokBarangByCabang(String nomorCabang) {
        List<StokBarang> stokBarangList = stokBarangRepository.findAll();
        
        return stokBarangList.stream()
                .filter(stok -> stok.getNomorCabang().equals(nomorCabang))
                .map(this::mapToStokBarangDetailDTO)
                .collect(Collectors.toList());
    }

    @Override
    public StokBarang getStokBarangByKodeAndCabang(Integer kodeBarang, String nomorCabang) {
        List<StokBarang> stokBarangList = stokBarangRepository.findAll();
        
        return stokBarangList.stream()
                .filter(stok -> stok.getKodeBarang().equals(kodeBarang) && 
                               stok.getNomorCabang().equals(nomorCabang))
                .findFirst()
                .orElse(null);
    }
    
    private StokBarangDetailDTO mapToStokBarangDetailDTO(StokBarang stokBarang) {
        StokBarangDetailDTO dto = new StokBarangDetailDTO();
        dto.setKodeBarang(stokBarang.getKodeBarang());
        dto.setNamaBarang(stokBarang.getNamaBarang());
        dto.setKategoriBarang(stokBarang.getKategoriBarang());
        dto.setHargaBarang(stokBarang.getHargaBarang());
        dto.setBentuk(stokBarang.getBentuk());
        dto.setStokBarang(stokBarang.getStokBarang());
        dto.setNomorCabang(stokBarang.getNomorCabang());
        
  
        if ("001".equals(stokBarang.getNomorCabang())) {
            dto.setNamaCabang("Pusat");
        } else {
            dto.setNamaCabang("Cabang " + stokBarang.getNomorCabang());
        }
        
        return dto;
    }
}