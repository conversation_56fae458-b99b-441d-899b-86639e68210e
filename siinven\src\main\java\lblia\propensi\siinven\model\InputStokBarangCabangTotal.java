package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="input_stok_barang_cabang_total")
public class InputStokBarangCabangTotal {
    @Id
    private String idPengajuan;

    @Column(name = "totalHarga", nullable = true)
    private Double totalHarga;

    @Column(name = "step", nullable = true)
    private int step;

    @Column(name="waktu_pengajuan", nullable = true)
    private LocalDateTime waktuPengajuan;

    @Column(name = "nomor_cabang_asal", nullable = true)
    private String nomorCabangAsal;

    @Column(name = "nomor_cabang_tujuan", nullable = true)
    private String nomorCabangTujuan;

    @Column(name = "flag_cabang", nullable = true)
    private Boolean flagCabang; //True : Asli False : Ke<PERSON><PERSON>
}
