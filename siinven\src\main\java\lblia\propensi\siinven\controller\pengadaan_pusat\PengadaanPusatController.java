package lblia.propensi.siinven.controller.pengadaan_pusat;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lblia.propensi.siinven.dto.request.pusat.InputStokBarangListRequest;
import lblia.propensi.siinven.dto.request.pusat.PersetujuanInputBarangPusatRequest;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.CopyBarangResponse;
import lblia.propensi.siinven.dto.response.pusat.InputStokBarangTotalResponse;
import lblia.propensi.siinven.repository.InputStokBarangPusatDb;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import lblia.propensi.siinven.service.InputStokBarangService;
import lblia.propensi.siinven.service.TrenPermintaanBukuService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/api/pengadaan-pusat")
@Tag(name = "Pengadaan Pusat", description = "APIs untuk pengadaan pusat")
public class PengadaanPusatController {

    private static final Logger logger = LoggerFactory.getLogger(PengadaanPusatController.class);

    private final JwtUtils jwtUtils;
    private final InputStokBarangService inputStokBarangService;

    @Autowired
    private TrenPermintaanBukuService trenPermintaanBukuService;

    public PengadaanPusatController(JwtUtils jwtUtils,
                                    InputStokBarangService inputStokBarangService) {
        this.jwtUtils = jwtUtils;
        this.inputStokBarangService = inputStokBarangService;
    }

    @GetMapping("/get-all-pengajuan")
    @Operation(summary= "get all pengajuan", description = "Endpoint untuk mendapatkan semua pengajuan")
    public ResponseEntity<?> getAllPengajuan() {
        try {
            BaseResponseDTO response = new BaseResponseDTO<>();

            List<HashMap<String, String>> pengajuanList = inputStokBarangService.getAllPengajuan();

            if (pengajuanList.isEmpty()) {
                response.setStatus(404);
                response.setMessage("No pengajuan found");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            response.setStatus(200);
            response.setMessage("Success");
            response.setData(pengajuanList);

            //request model for adding stock
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to get pengajuan");
            return ResponseEntity.status(500).body(response);
        }
    }

    @GetMapping("/get-stock")
    @Operation(summary= "get stock", description = "Endpoint untuk mendapatkan stock")
    public ResponseEntity<?> getStock() {

        try {
            BaseResponseDTO response = new BaseResponseDTO<>();
            System.out.println("masuk get stock");
            List<CopyBarangResponse> copyBarangList = inputStokBarangService.getAllBarang();

            if (copyBarangList.isEmpty()) {
                response.setStatus(404);
                response.setMessage("No stock found");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            response.setStatus(200);
            response.setMessage("Success");
            response.setData(copyBarangList);

            //request model for adding stock
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to get stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/add-stock")
    @Operation(summary= "add stock", description = "Endpoint untuk menambah stock")
    public ResponseEntity<?> addStock(@RequestHeader("Authorization") String token,
                                      @RequestBody InputStokBarangListRequest listRequest) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }
            BaseResponseDTO response = new BaseResponseDTO<>();
            System.out.println("masuk add stock");
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if(!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Gudang Pelaksana Umum")) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            System.out.println("Authorized");

            InputStokBarangTotalResponse inputStokBarangList = inputStokBarangService.inputStokBarang(listRequest);
            System.out.println("inputStokBarangList: " + inputStokBarangList);
            if (inputStokBarangList == null) {
                response.setStatus(400);
                response.setMessage("Failed to add stock");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            response.setStatus(201);
            response.setMessage("Created");
            response.setData(inputStokBarangList);

            return new ResponseEntity<>(response, HttpStatus.CREATED);


        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/revisi-stock")
    @Operation(summary= "Revisi stock", description = "Endpoint untuk menambah stock")
    public ResponseEntity<?> revisiStock(@RequestHeader("Authorization") String token, @RequestBody InputStokBarangListRequest listRequest) {


        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }
            BaseResponseDTO response = new BaseResponseDTO<>();
            System.out.println("masuk add stock");
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if(!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Gudang Pelaksana Umum")) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            System.out.println("Authorized");

            InputStokBarangTotalResponse inputStokBarangList = inputStokBarangService.inputStokBarang(listRequest);
            System.out.println("inputStokBarangList: " + inputStokBarangList);
            if (inputStokBarangList == null) {
                response.setStatus(400);
                response.setMessage("Failed to add stock");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            response.setStatus(200);
            response.setMessage("Success");
            response.setData(inputStokBarangList);

            return new ResponseEntity<>(response, HttpStatus.CREATED);


        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @GetMapping("/tabel-pengadaan/{idPengajuan}")
    @Operation(summary= "get tabel pengadaan", description = "Endpoint untuk mendapatkan tabel pengadaan")
    public ResponseEntity<?> getTabelPengadaan(@RequestHeader("Authorization") String token, @PathVariable("idPengajuan") String idPengajuan) {


        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }

            BaseResponseDTO response = new BaseResponseDTO<>();

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if (!Set.of("Admin", "Staf Gudang Pelaksana Umum", "Kepala Departemen SDM dan Umum", "Staf keuangan")
                    .contains(jwtUtils.getRolesFromJWT(token.substring(7)))) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            //request model for adding stock
            InputStokBarangTotalResponse inputStokBarangList = inputStokBarangService.getInputStokBarangById(idPengajuan);

            // create notifikasi

            if (inputStokBarangList == null) {
                response.setStatus(400);
                response.setMessage("No table provided");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            response.setStatus(200);
            response.setMessage("Success");
            response.setData(inputStokBarangList);

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/departemen-sdm")
    @Operation(summary= "Persetujuan oleh kepala departemen SDM", description = "Endpoint untuk persetujuan pengadaan oleh kepala departemen SDM")
    public ResponseEntity<?> persetujuanKepalaDepartemenSDM(@RequestHeader("Authorization") String token,
                                                            @RequestBody PersetujuanInputBarangPusatRequest pusatRequest) {
        try {

            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }
            BaseResponseDTO response = new BaseResponseDTO<>();

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Departemen SDM dan Umum")) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            //request model for agree
            Boolean result = inputStokBarangService.persetujuanInputBarangPusat(pusatRequest, "Kepala Departemen SDM dan Umum");

            if (result == null) {
                response.setStatus(400);
                response.setMessage("Failed to approve");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            response.setStatus(200);
            response.setMessage("Success");
            response.setData(result);

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/staf-keuangan")
    @Operation(summary= "Persetujuan oleh staf keuangan", description = "Endpoint untuk persetujuan pengadaan oleh staf keuangan")
    public ResponseEntity<?> persetujuanStafKeuangan(@RequestHeader("Authorization") String token,  @RequestBody PersetujuanInputBarangPusatRequest pusatRequest) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }

            BaseResponseDTO response = new BaseResponseDTO<>();

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf keuangan")) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            //request model for agree
            Boolean result = inputStokBarangService.persetujuanInputBarangPusat(pusatRequest, "Staf keuangan");

            if(!result) {
                response.setStatus(400);
                response.setMessage("Failed to approve");
                response.setData(result);

                return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
            } else {
                response.setStatus(200);
                response.setMessage("Success");
                response.setData(result);
                
                trenPermintaanBukuService.initializePermintaanBuku("001", pusatRequest.getIdPengajuan());
                return new ResponseEntity<>(response, HttpStatus.OK);
            }
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

}
