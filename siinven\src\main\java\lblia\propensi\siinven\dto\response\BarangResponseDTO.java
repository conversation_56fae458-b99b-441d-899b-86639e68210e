package lblia.propensi.siinven.dto.response;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BarangResponseDTO {
    private Integer kodeBarang;
    private String namaBarang;
    private String kategoriBarang;
    private Double hargaBarang;
    private String bentuk;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
