package lblia.propensi.siinven.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class KonfirmasiReturnRequestDTO {
    
    @NotNull(message = "Jumlah yang dikonfirmasi harus diisi")
    @PositiveOrZero(message = "Jumlah yang dikonfirmasi harus 0 atau lebih")
    private Integer jumlahDikonfirmasi;
}