package lblia.propensi.siinven.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BarangRequestDTO {
    @NotBlank
    private String namaBarang;

    @NotBlank
    private String kategoriBarang;

    @NotNull
    private Double hargaBarang;

    @NotNull
    @Pattern(regexp = "satuan|paket", message = "Bentuk harus 'satuan' atau 'paket'")
    private String bentuk;
}