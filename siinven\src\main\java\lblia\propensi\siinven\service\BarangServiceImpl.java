package lblia.propensi.siinven.service;

import jakarta.transaction.Transactional;
import lblia.propensi.siinven.dto.request.BarangRequestDTO;
import lblia.propensi.siinven.dto.response.BarangResponseDTO;
import lblia.propensi.siinven.model.Barang;
import lblia.propensi.siinven.repository.BarangDb;

import java.util.stream.Collectors;
import java.time.LocalDateTime;
import java.util.List;
import java.util.NoSuchElementException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class BarangServiceImpl implements BarangService {

    @Autowired
    private BarangDb barangDb;

    @Override
    public Barang findBarangByNamaBarang(String namaBarang) {
        return barangDb.findByNamaBarang(namaBarang);
    }

    @Override
    public List<BarangResponseDTO> getAllBarang() {
        List<Barang> barangList = barangDb.findAllActiveBranches();
        return barangList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public BarangResponseDTO getBarangById(Integer id) {
        Barang barang = barangDb.findById(id)
                .orElseThrow(() -> new NoSuchElementException("Barang dengan kode " + id + " tidak ditemukan"));

        if (barang.getDeletedAt() != null) {
            throw new NoSuchElementException("Barang dengan kode " + id + " sudah dihapus");
        }

        return convertToDTO(barang);
    }


    @Override
    public BarangResponseDTO createBarang(BarangRequestDTO barangRequestDTO) {
        Barang barang = new Barang();
        barang.setNamaBarang(barangRequestDTO.getNamaBarang());
        barang.setKategoriBarang(barangRequestDTO.getKategoriBarang());
        barang.setHargaBarang(barangRequestDTO.getHargaBarang());
        barang.setBentuk(barangRequestDTO.getBentuk());

        Barang savedBarang = barangDb.save(barang);

        return convertToDTO(savedBarang);
    }

    @Override
    public BarangResponseDTO updateBarang(Integer id, BarangRequestDTO barangRequestDTO) {
        Barang barang = barangDb.findById(id)
                .orElseThrow(() -> new NoSuchElementException("Barang dengan kode " + id + " tidak ditemukan"));

        if (barang.getDeletedAt() != null) {
            throw new NoSuchElementException("Barang dengan kode " + id + " sudah dihapus");
        }

        barang.setNamaBarang(barangRequestDTO.getNamaBarang());
        barang.setKategoriBarang(barangRequestDTO.getKategoriBarang());
        barang.setHargaBarang(barangRequestDTO.getHargaBarang());
        barang.setBentuk(barangRequestDTO.getBentuk());
        Barang updatedBarang = barangDb.save(barang);
        return convertToDTO(updatedBarang);
    }


    @Override
    public void deleteBarang(Integer id) {
        Barang barang = barangDb.findById(id)
            .orElseThrow(() -> new NoSuchElementException("Barang dengan kode " + id + " tidak ditemukan"));           
        if (barang.getDeletedAt() != null) {
            throw new NoSuchElementException("Barang dengan kode " + id + " sudah dihapus");
        }
        barang.setDeletedAt(LocalDateTime.now());
        barangDb.save(barang);
    }

    // Helper method to convert Barang entity to BarangResponseDTO
    private BarangResponseDTO convertToDTO(Barang barang) {
        BarangResponseDTO responseDTO = new BarangResponseDTO();
        responseDTO.setKodeBarang(barang.getKodeBarang());
        responseDTO.setNamaBarang(barang.getNamaBarang());
        responseDTO.setKategoriBarang(barang.getKategoriBarang());
        responseDTO.setHargaBarang(barang.getHargaBarang());
        responseDTO.setBentuk(barang.getBentuk());
        responseDTO.setCreatedAt(barang.getCreatedAt());
        responseDTO.setUpdatedAt(barang.getUpdatedAt());
        return responseDTO;
    }


}