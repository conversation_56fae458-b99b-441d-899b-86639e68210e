package lblia.propensi.siinven.dto.request.CabangKerjaSama;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CabangKerjaSamaRequestDTO {
    @NotNull(message = "Nomor cabang kerja sama tidak boleh kosong")
    private String nomorCabang;

    @NotBlank(message = "Nama Mitra tidak boleh blank")
    private String namaMitra;
    
    @NotBlank(message = "Alamat tidak boleh blank")
    private String alamat;
    
    @NotBlank(message = "Kontak tidak boleh blank")
    private String kontak;
    
    @NotBlank(message = "Jumlah Karyawan tidak boleh blank")
    @Min(value = 1, message = "Jumlah Karyawan harus lebih dari 0")
    private int jumlahKaryawan;
    
    @NotBlank(message = "Jam Operasional tidak boleh blank")
    private String jamOperasional;
    
    @NotBlank(message = "Masa Berlaku Kontrak tidak boleh blank")
    private String masaBerlakuKontrak;
    
    @NotNull(message = "Seorang karyawan harus dipilih sebagai Kepala Operasional Cabang")
    private String idKaryawanKepalaOperasional;
}
