package lblia.propensi.siinven.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import lblia.propensi.siinven.model.Notifikasi;

@Repository
public interface NotifikasiDb extends JpaRepository<Notifikasi, String> {
    List<Notifikasi> findByRolePenerimaAndNomorCabang(String rolePenerima, String nomorCabang);
    List<Notifikasi> findByIdPengajuan(String idPengajuan);
    List<Notifikasi> findByRolePenerima(String rolePenerima);
    List<Notifikasi> findByNomorCabang(String nomorCabang);
}
