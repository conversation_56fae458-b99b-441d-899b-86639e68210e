package lblia.propensi.siinven.controller;

import lblia.propensi.siinven.dto.request.EditStokBarangRequest;
import lblia.propensi.siinven.dto.response.EditStokBarangResponse;
import lblia.propensi.siinven.dto.response.StokBarangDetailDTO;
import lblia.propensi.siinven.service.EditStokBarangService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/stok")
public class EditStokBarangController {

    @Autowired
    private EditStokBarangService editStokBarangService;


    @GetMapping("/cabang/{nomorCabang}")
    public ResponseEntity<List<StokBarangDetailDTO>> getStokByCabang(@PathVariable String nomorCabang) {
        return ResponseEntity.ok(editStokBarangService.getStokBarangByCabang(nomorCabang));
    }

    @GetMapping("/detail/{kodeBarang}/{nomorCabang}")
    public ResponseEntity<?> getStokBarangDetail(@PathVariable Integer kodeBarang, @PathVariable String nomorCabang) {
        var stokBarang = editStokBarangService.getStokBarangByKodeAndCabang(kodeBarang, nomorCabang);
        if (stokBarang == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Barang tidak ditemukan");
        }
        
        StokBarangDetailDTO dto = new StokBarangDetailDTO();
        dto.setKodeBarang(stokBarang.getKodeBarang());
        dto.setNamaBarang(stokBarang.getNamaBarang());
        dto.setKategoriBarang(stokBarang.getKategoriBarang());
        dto.setHargaBarang(stokBarang.getHargaBarang());
        dto.setBentuk(stokBarang.getBentuk());
        dto.setStokBarang(stokBarang.getStokBarang());
        dto.setNomorCabang(stokBarang.getNomorCabang());
        
    
        if ("001".equals(stokBarang.getNomorCabang())) {
            dto.setNamaCabang("Pusat");
        } else {
            dto.setNamaCabang("Cabang " + stokBarang.getNomorCabang());
        }
        
        return ResponseEntity.ok(dto);
    }

    @PutMapping("/edit")
    public ResponseEntity<EditStokBarangResponse> editStokBarang(
            @RequestBody EditStokBarangRequest request,
            Authentication authentication) {
        
        String username = authentication.getName();
        EditStokBarangResponse response = editStokBarangService.editStokBarang(request, username);
        
        if (response.getSukses()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
}