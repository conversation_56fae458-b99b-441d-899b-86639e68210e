package lblia.propensi.siinven.controller;



import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.model.StokCabang;
import lblia.propensi.siinven.service.StokCabangService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/home/<USER>")
public class StokCabangRestController {

    @Autowired
    private StokCabangService stokCabangService;

    @GetMapping
    public ResponseEntity<BaseResponseDTO<List<StokCabang>>> getAllStokCabang() {
        return ResponseEntity.ok(stokCabangService.getAllStokCabang());
    }

    @PostMapping("/add")
    public ResponseEntity<BaseResponseDTO<StokCabang>> addStokCabang(@RequestBody StokCabang stokCabang) {
        return new ResponseEntity<>(stokCabangService.addStokCabang(stokCabang), HttpStatus.CREATED);
    }

    @GetMapping("/cabang/data")
    public BaseResponseDTO<StokCabang> getCabangData() {
        StokCabang data = stokCabangService.getCabangData();
        return new BaseResponseDTO<>(
            200,
            "Data stok cabang berhasil ditemukan",
            new Date(),
            data
        );
        
    }

}
