package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.CabangAsliDTO;
import lblia.propensi.siinven.dto.response.CabangAsliResponseDTO;
import lblia.propensi.siinven.model.CabangAsli;

import java.util.List;

public interface CabangAsliService {
    List<CabangAsliResponseDTO> getAllCabangAsli();
    CabangAsliResponseDTO getCabangAsliById(String id);
    CabangAsliResponseDTO createCabangAsli(CabangAsliDTO cabangAsliDTO);
    CabangAsliResponseDTO updateCabangAsli(String id, CabangAsliDTO cabangAsliDTO);
    void deleteCabangAsli(String id);
}