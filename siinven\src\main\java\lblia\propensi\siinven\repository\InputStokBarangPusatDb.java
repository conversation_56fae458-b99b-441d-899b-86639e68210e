package lblia.propensi.siinven.repository;

import lblia.propensi.siinven.model.InputStokBarangPusat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InputStokBarangPusatDb extends JpaRepository<InputStokBarangPusat, String> {
    @Query("SELECT p FROM InputStokBarangPusat p WHERE p.idPengajuan = :idPengajuan")
    List<InputStokBarangPusat> findAllByIdPengajuan(@Param("idPengajuan") String idPengajuan);

    //Fitur barang dengan stok menipis
    List<InputStokBarangPusat> findByStokBarangSaatIniLessThanEqual(Integer threshold);
    @Query("SELECT s FROM InputStokBarangPusat s WHERE s.stokBarangSaatIni BETWEEN :min AND :max")
    List<InputStokBarangPusat> findByStokBarangSaatIniBetween(
        @Param("min") Integer min, 
        @Param("max") Integer max
    );
}
