package lblia.propensi.siinven.repository;

import lblia.propensi.siinven.model.Pengguna;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PenggunaDb extends JpaRepository<Pengguna, String> {
    @Query("SELECT p FROM Pengguna p WHERE p.username = :username")
    Pengguna findByUsername(@Param("username") String username);

    @Query("SELECT p FROM Pengguna p WHERE p.idKaryawan = :idKaryawan")
    Pengguna findByIdKaryawan(@Param("idKaryawan") String idKaryawan);

    List<Pengguna> findAllByNomorCabang(String nomorCabang);

    List<Pengguna> findByRole(String role);
}
