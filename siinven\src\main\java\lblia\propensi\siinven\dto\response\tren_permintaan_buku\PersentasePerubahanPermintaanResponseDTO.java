package lblia.propensi.siinven.dto.response.tren_permintaan_buku;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersentasePerubahanPermintaanResponseDTO {
    private String monthYearString; // Misal "JANUARY-2025 ke MARCH-2025"
    private String monthYear; // Misal "1-2025 ke 3-2025"
    private Double persentasePerubahan; // Represents the percentage
    private String namaBarang; // Represents the book title
    private String nomorCabang; // Represents the branch number
}