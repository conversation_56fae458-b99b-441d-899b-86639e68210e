package lblia.propensi.siinven.service;

import java.util.List;

import lblia.propensi.siinven.dto.request.NotifikasiRequestDTO;
import lblia.propensi.siinven.dto.response.NotifikasiResponseDTO;
import lblia.propensi.siinven.model.Notifikasi;


public interface NotifikasiRestService {

    NotifikasiResponseDTO createNotifikasi(NotifikasiRequestDTO notifikasiRequestDTO);
    List<NotifikasiResponseDTO> getNotifikasiByRoleDanCabang(String rolePenerima, String nomorCabang);
    List<NotifikasiResponseDTO> getNotifikasiByPengajuan(String idPengajuan);
    void kirimNotifikasi(String rolePengirim, String rolePenerima, String nomorCabang, String isiNotifikasi, String idPengajuan);
    Notifikasi simpanNotifikasi(Notifikasi notifikasi);
    List<NotifikasiResponseDTO> getNotifikasiByRolePenerima(String rolePenerima);
    List<NotifikasiResponseDTO> getNotifikasiByNomorCabang(String nomorCabang);
    List<NotifikasiResponseDTO> getAllNotifikasi() ;
}