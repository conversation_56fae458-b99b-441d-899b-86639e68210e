package lblia.propensi.siinven.model;

import java.util.List;
import io.jsonwebtoken.lang.Arrays;
import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.JoinColumn;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "stok_cabang")
public class StokCabang {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer kodeBarang;

    @Column(nullable = false)
    private String namaBarang;

    @Column(nullable = false)
    private String kategoriBarang;

    @Column(nullable = false)
    private Double hargaBarang;

    @Column(nullable = false)
    private String bentuk;

    @Column(nullable = false)
    private Integer stokBarang;
    
}
