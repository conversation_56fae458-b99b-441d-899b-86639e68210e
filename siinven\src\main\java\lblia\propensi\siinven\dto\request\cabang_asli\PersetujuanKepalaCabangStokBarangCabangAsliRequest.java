package lblia.propensi.siinven.dto.request.cabang_asli;

import com.fasterxml.jackson.annotation.JsonInclude;
import lblia.propensi.siinven.dto.request.pusat.InputStokBarangRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersetujuanKepalaCabangStokBarangCabangAsliRequest {
    private PersetujuanStokBarangCabangAsliRequest persetujuan;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<InputStokBarangRequest> listInputStokBarang;
}
