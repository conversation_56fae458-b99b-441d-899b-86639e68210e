package lblia.propensi.siinven.service;

import jakarta.validation.constraints.NotNull;
import lblia.propensi.siinven.dto.response.StokMenipisResponseDTO;
import lblia.propensi.siinven.model.Barang;
import lblia.propensi.siinven.model.InputStokBarangPusat;
import lblia.propensi.siinven.repository.BarangDb;
import lblia.propensi.siinven.repository.InputStokBarangPusatDb;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StokMenipisServiceImpl implements StokMenipisService {

    // Threshold untuk menentukan stok KRITIS dan WARNING
    private static final int KRITIS_THRESHOLD = 5;    // ≤ 5 = KRITIS (merah)
    private static final int WARNING_THRESHOLD = 10;   // 6-10 = WARNING (kuning)

    @Autowired
    private InputStokBarangPusatDb stokPusatRepo;

    @Autowired
    private BarangDb barangRepo;

    @Override
    public List<StokMenipisResponseDTO> getAllStokMenipis() {
        return processStokData(
            stokPusatRepo.findByStokBarangSaatIniLessThanEqual(WARNING_THRESHOLD)
        );
    }

    @Override
    public List<StokMenipisResponseDTO> getStokKritis() {
        return processStokData(
            stokPusatRepo.findByStokBarangSaatIniLessThanEqual(KRITIS_THRESHOLD)
        );
    }

    @Override
    public List<StokMenipisResponseDTO> getStokWarning() {
        return processStokData(
            stokPusatRepo.findByStokBarangSaatIniBetween(
                KRITIS_THRESHOLD + 1, 
                WARNING_THRESHOLD
            )
        );
    }

    @Override
    public List<StokMenipisResponseDTO> filterByKategori(String kategori) {
        List<InputStokBarangPusat> stokMenipis = stokPusatRepo
            .findByStokBarangSaatIniLessThanEqual(WARNING_THRESHOLD);

        return processStokData(stokMenipis).stream()
            .filter(dto -> dto.getKategoriBarang().equalsIgnoreCase(kategori))
            .collect(Collectors.toList());
    }

    @Override
    public List<String> getAllKategori() {
        return barangRepo.findAllActiveBranches().stream()
            .map(Barang::getKategoriBarang)
            .distinct()
            .collect(Collectors.toList());
    }

    // ===== HELPER METHOD =====
    private List<StokMenipisResponseDTO> processStokData(List<InputStokBarangPusat> stokList) {
        // Mapping kode barang ke kategori untuk optimasi
       Map<Integer, String> kategoriMap = barangRepo.findAllActiveBranches().stream()
            .collect(Collectors.toMap(
                Barang::getKodeBarang,
                Barang::getKategoriBarang
            ));

        return stokList.stream()
            .map(stok -> {
                StokMenipisResponseDTO dto = new StokMenipisResponseDTO();
                dto.setKodeBarang(Integer.valueOf(stok.getKodeBarang()));
                dto.setNamaBarang(stok.getNamaBarang());
                dto.setStokTerkini(stok.getStokBarangSaatIni());
                dto.setKategoriBarang(
                    kategoriMap.getOrDefault(stok.getKodeBarang(), "Tidak Terkategori")
                );
                dto.setStatus(
                    stok.getStokBarangSaatIni() <= KRITIS_THRESHOLD ? "KRITIS" : "WARNING"
                );
                return dto;
            })
            .collect(Collectors.toList());
    }
}