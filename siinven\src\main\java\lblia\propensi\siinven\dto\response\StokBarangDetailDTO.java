package lblia.propensi.siinven.dto.response;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class StokBarangDetailDTO {
    private Integer kodeBarang;
    private String namaBarang;
    private String kategoriBarang;
    private Double hargaBarang;
    private String bentuk;
    private Integer stokBarang;
    private String nomorCabang;
    private String namaCabang;
}