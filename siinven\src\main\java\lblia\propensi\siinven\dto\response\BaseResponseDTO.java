package lblia.propensi.siinven.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BaseResponseDTO<T> {
    private Integer status;
    private String message;
    private Date timestamp = new Date(); // Default timestamp
    private T data;
}


