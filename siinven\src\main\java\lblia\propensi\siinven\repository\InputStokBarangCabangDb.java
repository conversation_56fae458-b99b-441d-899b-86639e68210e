package lblia.propensi.siinven.repository;

import lblia.propensi.siinven.model.InputStokBarangCabang;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface InputStokBarangCabangDb extends JpaRepository<InputStokBarangCabang, String> {
    @Query("SELECT p FROM InputStokBarangCabang p WHERE p.idPengajuan = :idPengajuan")
    List<InputStokBarangCabang> findAllByIdPengajuan(@Param("idPengajuan") String idPengajuan);
}
