package lblia.propensi.siinven.dto.response;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class StokKurangResponseDTO {
    private String namaBarang;
    private Integer stokDiminta;
    private Integer stokTerkini;
    private String namaCabang;
    private String statusPermintaan; //valid, understocked, overstocked

    public static String determineStatus(int diminta, int terkini) {
        if (diminta > terkini) return "Understocked";
        else if (diminta < terkini * 0.5) return "Overstocked";
        return "Valid";
    }
}
