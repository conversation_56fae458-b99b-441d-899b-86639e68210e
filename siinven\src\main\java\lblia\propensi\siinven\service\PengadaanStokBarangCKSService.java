package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.cabang_asli.InputStokBarangCARequest;
import lblia.propensi.siinven.dto.request.cabang_asli.PersetujuanStokBarangCabangAsliRequest;
import lblia.propensi.siinven.dto.request.pusat.InputStokBarangRequest;
import lblia.propensi.siinven.dto.response.StokBarangResponseCabangDTO;
import lblia.propensi.siinven.dto.response.cabang_asli.PengadaanCabangAsliTotalResponse;

import java.util.HashMap;
import java.util.List;

public interface PengadaanStokBarangCKSService {
    List<StokBarangResponseCabangDTO> getAllBarang(String nomorCabang);
    List<HashMap<String, String>> getAllPengajuan();
    Boolean persetujuanInputBarangCabangAsli(PersetujuanStokBarangCabangAsliRequest request, String role);
    PengadaanCabangAsliTotalResponse inputStokBarangCabang(InputStokBarangCARequest listInputStokBarang);
    PengadaanCabangAsliTotalResponse getInputStokBarangByIdCabang(String idPengajuan);
    Boolean revisiInputStokBarangCabang(List<InputStokBarangRequest> listInputBarang,String nomorCabang, String idPengajuan);

    List<HashMap<String, String>> getPengajuanByCabang(String nomorCabang);
}
