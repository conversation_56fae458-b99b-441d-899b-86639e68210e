package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.BarangRequestDTO;
import lblia.propensi.siinven.dto.response.BarangResponseDTO;
import lblia.propensi.siinven.model.Barang;

import java.util.List;

public interface BarangService {
    Barang findBarangByNamaBarang(String namaBarang);
    List<BarangResponseDTO> getAllBarang();
    BarangResponseDTO getBarangById(Integer id);
    BarangResponseDTO createBarang(BarangRequestDTO barangRequestDTO);
    BarangResponseDTO updateBarang(Integer id, BarangRequestDTO barangRequestDTO);
    void deleteBarang(Integer id);
}