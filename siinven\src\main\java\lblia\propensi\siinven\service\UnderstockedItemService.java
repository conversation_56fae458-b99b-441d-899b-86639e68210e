package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.response.UnderstockedItemResponseDTO;

import java.util.List;
import java.util.Map;

public interface UnderstockedItemService {

    /**
     * Get all items with their stock status (valid or understocked)
     * @return List of items with their stock status
     */
    List<UnderstockedItemResponseDTO> getAllUnderstockedItems();

    /**
     * Get items with their stock status filtered by branch
     * @param nomorCabang Branch number
     * @return List of items with their stock status for the specified branch
     */
    List<UnderstockedItemResponseDTO> getUnderstockedItemsByCabang(String nomorCabang);

    /**
     * Get items with their stock status filtered by category
     * @param kategori Item category
     * @return List of items with their stock status for the specified category
     */
    List<UnderstockedItemResponseDTO> getUnderstockedItemsByKategori(String kategori);

    /**
     * Get items with their stock status filtered by branch and category
     * @param nomorCabang Branch number
     * @param kategori Item category
     * @return List of items with their stock status for the specified branch and category
     */
    List<UnderstockedItemResponseDTO> getUnderstockedItemsByCabangAndKategori(String nomorCabang, String kategori);

    /**
     * Get all categories for filtering
     * @return List of all categories
     */
    List<String> getAllKategori();

    /**
     * Get all branches for filtering
     * @return List of branch information with nomor and name
     */
    List<Map<String, String>> getAllCabang();
}

