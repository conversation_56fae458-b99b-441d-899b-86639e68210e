package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.response.StokMenipisResponseDTO;
import java.util.List;

public interface StokMenipisService {
    // Ambil semua stok menipis (kritis + warning)
    List<StokMenipisResponseDTO> getAllStokMenipis();
    // Filter by status
    List<StokMenipisResponseDTO> getStokKritis(); // Stok ≤ 5
    List<StokMenipisResponseDTO> getStokWarning(); // Stok 6-10
    // Filter by kategori
    List<StokMenipisResponseDTO> filterByKategori(String kategori);
    List<String> getAllKategori();  // Untuk dropdown filter frontend
}