package lblia.propensi.siinven.model;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="log_sistem")
public class LogSistem {
    @Id
    private String idLog;

    private String idKaryawan;

    private String rolePengguna;

    private String nomorCabang;

    private String aktivitas;
}
