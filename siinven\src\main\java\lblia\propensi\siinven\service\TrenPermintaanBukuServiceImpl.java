package lblia.propensi.siinven.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lblia.propensi.siinven.dto.request.cabang_asli.PersetujuanKepalaCabangStokBarangCabangAsliRequest;
import lblia.propensi.siinven.dto.request.pusat.InputStokBarangRequest;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.GrafikPermintaanBukuResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.PersentasePerubahanPermintaanResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.RataRataPemesananBukuResponseDTO;
import lblia.propensi.siinven.model.Barang;
import lblia.propensi.siinven.model.InputStokBarangCabang;
import lblia.propensi.siinven.model.InputStokBarangPusat;
import lblia.propensi.siinven.model.TrenPermintaanBuku;
import lblia.propensi.siinven.repository.BarangDb;
import lblia.propensi.siinven.repository.InputStokBarangCabangDb;
import lblia.propensi.siinven.repository.InputStokBarangPusatDb;
import lblia.propensi.siinven.repository.TrenPermintaanBukuDb;

@Service
public class TrenPermintaanBukuServiceImpl implements TrenPermintaanBukuService {
    @Autowired
    private TrenPermintaanBukuDb trenPermintaanBukuDb;

    @Autowired
    private InputStokBarangCabangDb inputStokBarangCabangDb;

    @Autowired
    private InputStokBarangPusatDb inputStokBarangPusatDb;

    @Autowired
    private BarangDb barangDb;

    @Override
    public void initializePermintaanBuku(String nomorCabang, String idPengajuan) {
        List<InputStokBarangCabang> listInputStokBarangCabang = inputStokBarangCabangDb.findAllByIdPengajuan(idPengajuan);
        List<InputStokBarangPusat> listInputStokBarangPusat = inputStokBarangPusatDb.findAllByIdPengajuan(idPengajuan);

        for (InputStokBarangCabang inputStokBarangCabang : listInputStokBarangCabang) {
            Integer kodeBarang = Integer.valueOf(inputStokBarangCabang.getKodeBarang());
            String kategoriBarang = barangDb.findActiveByKodeBarang(kodeBarang).getKategoriBarang();

            if (kategoriBarang.toLowerCase().equals("buku") || kategoriBarang.toLowerCase().equals("book")) {
                TrenPermintaanBuku permintaanBuku = new TrenPermintaanBuku();
                permintaanBuku.setKodeBarang(kodeBarang);
                permintaanBuku.setNamaBarang(inputStokBarangCabang.getNamaBarang());
                permintaanBuku.setKategoriBarang(kategoriBarang);
                permintaanBuku.setNomorCabang(nomorCabang);
                permintaanBuku.setStokInput(inputStokBarangCabang.getStokInput());
                permintaanBuku.setStokBarangSaatIni(inputStokBarangCabang.getStokBarangCabangSaatIni());
                permintaanBuku.setTanggalPemesanan(LocalDateTime.now());
                trenPermintaanBukuDb.save(permintaanBuku);
            }
        }

        for (InputStokBarangPusat inputStokBarangPusat : listInputStokBarangPusat) {
            Integer kodeBarang = Integer.valueOf(inputStokBarangPusat.getKodeBarang());
            String kategoriBarang = barangDb.findActiveByKodeBarang(kodeBarang).getKategoriBarang();

            if (kategoriBarang.toLowerCase().equals("buku") || kategoriBarang.toLowerCase().equals("book")) {
                TrenPermintaanBuku permintaanBuku = new TrenPermintaanBuku();
                permintaanBuku.setKodeBarang(kodeBarang);
                permintaanBuku.setNamaBarang(inputStokBarangPusat.getNamaBarang());
                permintaanBuku.setKategoriBarang(kategoriBarang);
                permintaanBuku.setNomorCabang(nomorCabang);
                permintaanBuku.setStokInput(inputStokBarangPusat.getStokInput());
                permintaanBuku.setStokBarangSaatIni(inputStokBarangPusat.getStokBarangSaatIni());
                permintaanBuku.setTanggalPemesanan(LocalDateTime.now());
                trenPermintaanBukuDb.save(permintaanBuku);
            }
        }
    }

    @Override
    public void initializePermintaanBuku(PersetujuanKepalaCabangStokBarangCabangAsliRequest request) {
        List<InputStokBarangRequest> listStokBarang = request.getListInputStokBarang();

        for (InputStokBarangRequest stokBarang : listStokBarang) {
            Barang barang = barangDb.findActiveByKodeBarang(stokBarang.getKodeBarang());
            String kategoriBarang = barang.getKategoriBarang();
            
            if (kategoriBarang.toLowerCase().equals("buku") || kategoriBarang.toLowerCase().equals("book")) {
                TrenPermintaanBuku permintaanBuku = new TrenPermintaanBuku();
                permintaanBuku.setKodeBarang(stokBarang.getKodeBarang());
                permintaanBuku.setNamaBarang(barang.getNamaBarang());
                permintaanBuku.setKategoriBarang(kategoriBarang);
                permintaanBuku.setNomorCabang(request.getPersetujuan().getNomorCabang());
                permintaanBuku.setStokInput(stokBarang.getStokInput());
                permintaanBuku.setStokBarangSaatIni(0);
                permintaanBuku.setTanggalPemesanan(LocalDateTime.now());
                trenPermintaanBukuDb.save(permintaanBuku);
            }
        }
    }

    @Override
    public List<GrafikPermintaanBukuResponseDTO> getGrafikPermintaanBuku(LocalDate startDate, LocalDate endDate, String nomorCabang) {
        List<TrenPermintaanBuku> data = trenPermintaanBukuDb.findByFilters(startDate.atTime(LocalTime.MIN), endDate.atTime(LocalTime.MAX), nomorCabang);
        
        // Sort data by tanggalPemesanan
        List<TrenPermintaanBuku> sortedData = data.stream()
            .sorted(Comparator.comparing(TrenPermintaanBuku::getTanggalPemesanan))
            .collect(Collectors.toList());
    
        // Group by month-year
        Map<String, List<TrenPermintaanBuku>> groupedByMonth = sortedData.stream()
            .collect(Collectors.groupingBy(
                d -> d.getTanggalPemesanan().getMonthValue() + "-" + d.getTanggalPemesanan().getYear(),
                LinkedHashMap::new, // Maintain order
                Collectors.toList()
            ));
    
        List<GrafikPermintaanBukuResponseDTO> response = new ArrayList<>();
        
        for (Map.Entry<String, List<TrenPermintaanBuku>> entry : groupedByMonth.entrySet()) {
            String monthYear = entry.getKey();
            List<TrenPermintaanBuku> monthData = entry.getValue();
            
            // Calculate totalOrders and stokInput
            Integer stokInput = monthData.stream()
                .mapToInt(TrenPermintaanBuku::getStokInput)
                .sum();
            
            // Get the earliest date's stokBarangSaatIni and stokInput
            TrenPermintaanBuku earliestEntry = monthData.get(0); // Since data is sorted, the first entry is the earliest
            Integer stokBarangSaatIni = earliestEntry.getStokBarangSaatIni();
    
            // Add stokBarangSaatIni to totalOrders
            Integer totalOrders = stokBarangSaatIni + stokInput;

            response.add(new GrafikPermintaanBukuResponseDTO(monthYear, totalOrders, stokInput, stokBarangSaatIni, nomorCabang));
        }

        // Sort the result by nomorCabang, year, and month
        response.sort(Comparator.comparing(GrafikPermintaanBukuResponseDTO::getNomorCabang)
                .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split("-")[1])) // Year
                .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split("-")[0]))); // Month
        
        return response;
    }

    @Override
    public List<GrafikPermintaanBukuResponseDTO> getGrafikPermintaanBukuAll(LocalDate startDate, LocalDate endDate) {
        List<TrenPermintaanBuku> data = trenPermintaanBukuDb.findByFiltersAllCabang(startDate.atTime(LocalTime.MIN), endDate.atTime(LocalTime.MAX));
        
        // Sort data by tanggalPemesanan
        List<TrenPermintaanBuku> sortedData = data.stream()
            .sorted(Comparator.comparing(TrenPermintaanBuku::getTanggalPemesanan))
            .collect(Collectors.toList());
    
        // Group by month-year#nomorCabang
        Map<String, List<TrenPermintaanBuku>> groupedByMonthAndCabang = sortedData.stream()
            .collect(Collectors.groupingBy(
                d -> d.getTanggalPemesanan().getMonthValue() + "-" + d.getTanggalPemesanan().getYear() + "#" + d.getNomorCabang(),
                LinkedHashMap::new, // Maintain order
                Collectors.toList()
            ));
    
        List<GrafikPermintaanBukuResponseDTO> response = new ArrayList<>();
        
        for (Map.Entry<String, List<TrenPermintaanBuku>> entry : groupedByMonthAndCabang.entrySet()) {
            String[] parts = entry.getKey().split("#");
            String monthYear = parts[0];
            String nomorCabang = parts[1];
            List<TrenPermintaanBuku> monthData = entry.getValue();
            
            // Calculate totalOrders and stokInput
            Integer stokInput = monthData.stream()
                .mapToInt(TrenPermintaanBuku::getStokInput)
                .sum();
            
            // Get the earliest date's stokBarangSaatIni and stokInput
            TrenPermintaanBuku earliestEntry = monthData.get(0); // Since data is sorted, the first entry is the earliest
            Integer stokBarangSaatIni = earliestEntry.getStokBarangSaatIni();
    
            // Add stokBarangSaatIni to totalOrders
            Integer totalOrders = stokBarangSaatIni + stokInput;

            response.add(new GrafikPermintaanBukuResponseDTO(monthYear, totalOrders, stokInput, stokBarangSaatIni, nomorCabang));
        }

        // Sort the result by nomorCabang, year, and month
        response.sort(Comparator.comparing(GrafikPermintaanBukuResponseDTO::getNomorCabang)
                .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split("-")[1])) // Year
                .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split("-")[0]))); // Month
    
        return response;
    }

    @Override
    public List<RataRataPemesananBukuResponseDTO> getRataRataPemesanan(LocalDate startDate, LocalDate endDate, String nomorCabang) {
        List<TrenPermintaanBuku> data = trenPermintaanBukuDb.findByFilters(startDate.atTime(LocalTime.MIN), endDate.atTime(LocalTime.MAX), nomorCabang);
    
        // Grouping by month-year and book title
        Map<String, Map<String, List<TrenPermintaanBuku>>> groupedData = data.stream()
            .collect(Collectors.groupingBy(
                tren -> tren.getTanggalPemesanan().getMonthValue() + "-" + tren.getTanggalPemesanan().getYear(), // Month-Year
                Collectors.groupingBy(TrenPermintaanBuku::getNamaBarang) // Group by book title
            ));
    
        List<RataRataPemesananBukuResponseDTO> result = new ArrayList<>();
    
        for (Map.Entry<String, Map<String, List<TrenPermintaanBuku>>> monthEntry : groupedData.entrySet()) {
            String monthYear = monthEntry.getKey();
            String[] partsMonthYear = monthYear.split("-");
            String monthYearString = Month.of(Integer.valueOf(partsMonthYear[0])) + "-" + partsMonthYear[1];
            Map<String, List<TrenPermintaanBuku>> bookEntries = monthEntry.getValue();
    
            for (Map.Entry<String, List<TrenPermintaanBuku>> bookEntry : bookEntries.entrySet()) {
                String namaBarang = bookEntry.getKey();
                List<TrenPermintaanBuku> orders = bookEntry.getValue();
    
                // Calculate average and total orders
                double averageOrders = orders.stream().mapToInt(TrenPermintaanBuku::getStokInput).average().orElse(0);
                int totalOrders = orders.stream().mapToInt(TrenPermintaanBuku::getStokInput).sum();
    
                // Create DTO
                RataRataPemesananBukuResponseDTO dto = new RataRataPemesananBukuResponseDTO(monthYearString, monthYear, namaBarang, averageOrders, totalOrders, nomorCabang);
                result.add(dto);
            }
        }

        // Sort the result by nomorCabang, year, and month
        result.sort(Comparator.comparing(RataRataPemesananBukuResponseDTO::getNomorCabang)
                .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split("-")[1])) // Year
                .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split("-")[0]))); // Month
        
        return result;
    }

    @Override
    public List<RataRataPemesananBukuResponseDTO> getRataRataPemesananAll(LocalDate startDate, LocalDate endDate) {
        List<TrenPermintaanBuku> data = trenPermintaanBukuDb.findByFiltersAllCabang(startDate.atTime(LocalTime.MIN), endDate.atTime(LocalTime.MAX));
    
        // Grouping by month-year#nomorCabang and book title
        Map<String, Map<String, List<TrenPermintaanBuku>>> groupedDataAndCabang = data.stream()
            .collect(Collectors.groupingBy(
                tren -> tren.getTanggalPemesanan().getMonthValue() + "-" + tren.getTanggalPemesanan().getYear() + "#" + tren.getNomorCabang(), // Month-Year#NomorCabang
                Collectors.groupingBy(TrenPermintaanBuku::getNamaBarang) // Group by book title
            ));
    
        List<RataRataPemesananBukuResponseDTO> result = new ArrayList<>();
    
        for (Map.Entry<String, Map<String, List<TrenPermintaanBuku>>> monthEntry : groupedDataAndCabang.entrySet()) {
            String[] parts = monthEntry.getKey().split("#");
            String monthYear = parts[0];
            String nomorCabang = parts[1];
            String[] partsMonthYear = monthYear.split("-");
            String monthYearString = Month.of(Integer.valueOf(partsMonthYear[0])) + "-" + partsMonthYear[1];
            Map<String, List<TrenPermintaanBuku>> bookEntries = monthEntry.getValue();
    
            for (Map.Entry<String, List<TrenPermintaanBuku>> bookEntry : bookEntries.entrySet()) {
                String namaBarang = bookEntry.getKey();
                List<TrenPermintaanBuku> orders = bookEntry.getValue();
    
                // Calculate average and total orders
                double averageOrders = orders.stream().mapToInt(TrenPermintaanBuku::getStokInput).average().orElse(0);
                int totalOrders = orders.stream().mapToInt(TrenPermintaanBuku::getStokInput).sum();
    
                // Create DTO
                RataRataPemesananBukuResponseDTO dto = new RataRataPemesananBukuResponseDTO(monthYearString, monthYear, namaBarang, averageOrders, totalOrders, nomorCabang);
                result.add(dto);
            }
        }

        // Sort the result by nomorCabang, year, and month
        result.sort(Comparator.comparing(RataRataPemesananBukuResponseDTO::getNomorCabang)
                .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split("-")[1])) // Year
                .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split("-")[0]))); // Month
    
        return result;
    }
    
    @Override
    public List<PersentasePerubahanPermintaanResponseDTO> getPersentasePerubahanPermintaan(LocalDate startDate, LocalDate endDate) {
        List<TrenPermintaanBuku> data = trenPermintaanBukuDb.findByFiltersAllCabang(
            startDate.atTime(LocalTime.MIN),
            endDate.atTime(LocalTime.MAX)
        );

        // Group by "cabang#namaBarang"
        Map<String, List<TrenPermintaanBuku>> groupedByCabangAndBarang = data.stream()
            .collect(Collectors.groupingBy(tren -> tren.getNomorCabang() + "#" + tren.getNamaBarang()));

        List<PersentasePerubahanPermintaanResponseDTO> result = new ArrayList<>();

        for (Map.Entry<String, List<TrenPermintaanBuku>> entry : groupedByCabangAndBarang.entrySet()) {
            String[] keyParts = entry.getKey().split("#");
            String cabang = keyParts[0];
            String namaBarang = keyParts[1];

            // Group by month-year
            Map<String, Integer> monthToStokInput = entry.getValue().stream()
                .collect(Collectors.groupingBy(
                    tren -> {
                        LocalDate date = tren.getTanggalPemesanan().toLocalDate();
                        return String.format("%04d-%02d", date.getYear(), date.getMonthValue());
                    },
                    TreeMap::new, // ensure sorting by year-month
                    Collectors.summingInt(TrenPermintaanBuku::getStokInput)
                ));

            String previousMonthKey = null;
            Integer previousStokInput = null;

            for (Map.Entry<String, Integer> monthEntry : monthToStokInput.entrySet()) {
                String currentMonthKey = monthEntry.getKey(); // e.g., "2025-01"
                Integer currentStokInput = monthEntry.getValue();

                String[] parts = currentMonthKey.split("-");
                String currentMonthYearString = Month.of(Integer.parseInt(parts[1])) + "-" + parts[0];

                String monthYearStringFinal;
                String monthYearFinal;
                double percentageChange;

                if (previousMonthKey == null) {
                    // First entry
                    monthYearStringFinal = currentMonthYearString + " ke " + currentMonthYearString;
                    monthYearFinal = Integer.parseInt(parts[1]) + "-" + parts[0] + " ke " + Integer.parseInt(parts[1]) + "-" + parts[0];
                    percentageChange = 0.0;
                } else {
                    String[] prevParts = previousMonthKey.split("-");
                    String previousMonthYearString = Month.of(Integer.parseInt(prevParts[1])) + "-" + prevParts[0];
                    monthYearStringFinal = previousMonthYearString + " ke " + currentMonthYearString;
                    monthYearFinal = Integer.parseInt(prevParts[1]) + "-" + prevParts[0] + " ke " + Integer.parseInt(parts[1]) + "-" + parts[0];
                    percentageChange = previousStokInput != 0
                        ? (currentStokInput - previousStokInput) / (double) previousStokInput
                        : 0.0;
                }

                result.add(new PersentasePerubahanPermintaanResponseDTO(
                    monthYearStringFinal,
                    monthYearFinal,
                    percentageChange,
                    namaBarang,
                    cabang
                ));

                previousMonthKey = currentMonthKey;
                previousStokInput = currentStokInput;
            }
        }

        // Optional sort for final result
        result.sort(Comparator.comparing(PersentasePerubahanPermintaanResponseDTO::getNamaBarang)
            .thenComparing(PersentasePerubahanPermintaanResponseDTO::getNomorCabang)
            .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split(" ke ")[1].split("-")[1])) // Year
            .thenComparing(dto -> Integer.parseInt(dto.getMonthYear().split(" ke ")[1].split("-")[0]))); // Month

        return result;
    }
    
}
