package lblia.propensi.siinven.service;

import jakarta.transaction.Transactional;
import lblia.propensi.siinven.dto.response.LogBarangResponse;
import lblia.propensi.siinven.model.LogStokBarang;
import lblia.propensi.siinven.repository.LogStokBarangDb;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Service
@Transactional
public class LogStokBarangServiceImpl implements LogStokBarangService {
    private final LogStokBarangDb logStokBarangDb;

    public LogStokBarangServiceImpl(LogStokBarangDb logStokBarangDb) {
        this.logStokBarangDb = logStokBarangDb;
    }

    @Override
    public void createLogStokBarang(Integer kodeBarang, String nomorCabang, Integer stokSebelum, Integer stokSesudah, String idPengajuan, String keterangan) {
        try {
            LogStokBarang logStokBarang = new LogStokBarang();
            logStokBarang.setKodeBarang(kodeBarang);
            logStokBarang.setNomorCabang(nomorCabang);
            logStokBarang.setStokSebelum(stokSebelum);
            logStokBarang.setStokSesudah(stokSesudah);
            if (idPengajuan != null) {
                logStokBarang.setIdPengajuan(idPengajuan);
            }
            logStokBarang.setKeterangan(keterangan);
            logStokBarang.setStockDate(LocalDate.now());
            logStokBarangDb.save(logStokBarang);
        } catch (Exception e) {
            throw new RuntimeException("Error creating log stok barang: " + e.getMessage());
        }
    }

    @Override
    public List<LogBarangResponse> getLogStokBarang(Integer kodeBarang) {

        List<LogStokBarang> logStokBarangList = logStokBarangDb.findAll();
        List<LogBarangResponse> logBarangResponseList = new ArrayList<>();

        // Optional: Define a specific date formatter
        // DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        // If you use .toString() on LocalDate, it defaults to ISO_LOCAL_DATE (YYYY-MM-DD)

        for (LogStokBarang logStokBarang : logStokBarangList) {
            if (logStokBarang.getKodeBarang().equals(kodeBarang)) {
                LogBarangResponse logBarangResponse = new LogBarangResponse();
                logBarangResponse.setIdLog(logStokBarang.getId());
                logBarangResponse.setKodeBarang(logStokBarang.getKodeBarang());
                logBarangResponse.setNomorCabang(logStokBarang.getNomorCabang());
                logBarangResponse.setStokSebelum(logStokBarang.getStokSebelum());
                logBarangResponse.setStokSesudah(logStokBarang.getStokSesudah());
                if (logStokBarang.getIdPengajuan() != null) {
                    logBarangResponse.setIdPengajuan(logStokBarang.getIdPengajuan());
                }
                logBarangResponse.setKeterangan(logStokBarang.getKeterangan());

                // 1. Convert LocalDate to String
                if (logStokBarang.getStockDate() != null) {
                    // Using default YYYY-MM-DD format
                    logBarangResponse.setWaktuLog(logStokBarang.getStockDate().toString());
                    // Or with a custom formatter:
                    // logBarangResponse.setWaktuLog(logStokBarang.getStockDate().format(formatter));
                } else {
                    logBarangResponse.setWaktuLog(null); // Or some default string like "N/A"
                }
                logBarangResponseList.add(logBarangResponse);
            }
        }

        // 2. Sort the response list to newest first (descending order of waktuLog)
        // The default LocalDate.toString() format "YYYY-MM-DD" is lexicographically sortable.
        logBarangResponseList.sort(new Comparator<LogBarangResponse>() {
            @Override
            public int compare(LogBarangResponse o1, LogBarangResponse o2) {
                // Handle cases where waktuLog might be null to avoid NullPointerException
                if (o1.getWaktuLog() == null && o2.getWaktuLog() == null) {
                    return 0;
                }
                if (o1.getWaktuLog() == null) {
                    return 1; // Treat nulls as older, so they go to the end
                }
                if (o2.getWaktuLog() == null) {
                    return -1; // Treat nulls as older
                }
                // For descending order (newest first), compare o2 with o1
                return o2.getWaktuLog().compareTo(o1.getWaktuLog());
            }
        });

        // Alternatively, using Java 8 Lambda and Comparator helpers:
        // logBarangResponseList.sort(
        //     Comparator.comparing(LogBarangResponse::getWaktuLog, Comparator.nullsLast(Comparator.reverseOrder()))
        // );
        // If you are certain waktuLog will never be null after conversion, it's simpler:
        // logBarangResponseList.sort(Comparator.comparing(LogBarangResponse::getWaktuLog).reversed());


        return logBarangResponseList;
    }
}
