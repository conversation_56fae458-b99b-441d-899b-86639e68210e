package lblia.propensi.siinven.service;

import com.github.javafaker.Faker;
import lblia.propensi.siinven.dto.request.CabangAsliDTO;
import lblia.propensi.siinven.dto.response.CabangAsliResponseDTO;
import lblia.propensi.siinven.model.CabangAsli;
import lblia.propensi.siinven.repository.CabangAsliDb;
import lblia.propensi.siinven.repository.PenggunaDb;
import lblia.propensi.siinven.model.Pengguna;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Time;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;
import java.util.Locale;

@Service
public class CabangAsliRestServiceImpl implements CabangAsliService {

    @Autowired
    private CabangAsliDb cabangAsliDb;
    
    @Autowired
    private PenggunaDb userRepository;

    private Faker faker;

    public CabangAsliRestServiceImpl() {
        this.faker = new Faker(new Locale("id-ID"));
    }

    @Override
    public List<CabangAsliResponseDTO> getAllCabangAsli() {
        List<CabangAsli> cabangAsliList = cabangAsliDb.findAllActiveBranches();
        return cabangAsliList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public CabangAsliResponseDTO getCabangAsliById(String id) {
        CabangAsli cabangAsli = cabangAsliDb.findById(id)
                .orElseThrow(() -> new NoSuchElementException("Cabang Asli dengan ID " + id + " tidak ditemukan"));

        if (cabangAsli.getDeletedAt() != null) {
            throw new NoSuchElementException("Cabang Asli dengan ID " + id + " sudah dihapus");
        }

        return convertToDTO(cabangAsli);
    }

        // @Override
        // public CabangAsliResponseDTO createCabangAsli(CabangAsliDTO cabangAsliDTO) {
        //     CabangAsli cabangAsli = new CabangAsli();
        //     // Menggunakan nomorCabang dari input user, bukan generate
        //     cabangAsli.setNomorCabang(cabangAsliDTO.getNomorCabang());
        //     cabangAsli.setNamaCabang(cabangAsliDTO.getNamaCabang());
        //     cabangAsli.setAlamat(cabangAsliDTO.getAlamat());
        //     cabangAsli.setKontak(cabangAsliDTO.getKontak());
        //     cabangAsli.setJumlahKaryawan(cabangAsliDTO.getJumlahKaryawan());
        //     cabangAsli.setJamOperasional(cabangAsliDTO.getJamOperasional());
        //     cabangAsli.setIdKepalaOperasional(cabangAsliDTO.getIdKepalaOperasional());

        //     CabangAsli savedCabangAsli = cabangAsliDb.save(cabangAsli);
        //     return convertToDTO(savedCabangAsli);
        // }

        // @Override
        // public CabangAsliResponseDTO updateCabangAsli(String id, CabangAsliDTO cabangAsliDTO) {
        //     CabangAsli cabangAsli = cabangAsliDb.findById(id)
        //             .orElseThrow(() -> new NoSuchElementException("Cabang Asli dengan ID " + id + " tidak ditemukan"));

        //     if (cabangAsli.getDeletedAt() != null) {
        //         throw new NoSuchElementException("Cabang Asli dengan ID " + id + " sudah dihapus");
        //     }

        //     cabangAsli.setNamaCabang(cabangAsliDTO.getNamaCabang());
        //     cabangAsli.setAlamat(cabangAsliDTO.getAlamat());
        //     cabangAsli.setKontak(cabangAsliDTO.getKontak());
        //     cabangAsli.setJumlahKaryawan(cabangAsliDTO.getJumlahKaryawan());
        //     cabangAsli.setJamOperasional(cabangAsliDTO.getJamOperasional());
        //     cabangAsli.setIdKepalaOperasional(cabangAsliDTO.getIdKepalaOperasional());

        //     CabangAsli updatedCabangAsli = cabangAsliDb.save(cabangAsli);
        //     return convertToDTO(updatedCabangAsli);
        // }

        @Override
        public CabangAsliResponseDTO createCabangAsli(CabangAsliDTO cabangAsliDTO) {
            // Validasi Kepala Operasional
            validateKepalaOperasional(cabangAsliDTO.getIdKepalaOperasional());

            CabangAsli cabangAsli = new CabangAsli();
            cabangAsli.setNomorCabang(cabangAsliDTO.getNomorCabang());
            cabangAsli.setNamaCabang(cabangAsliDTO.getNamaCabang());
            cabangAsli.setAlamat(cabangAsliDTO.getAlamat());
            cabangAsli.setKontak(cabangAsliDTO.getKontak());
            cabangAsli.setJumlahKaryawan(cabangAsliDTO.getJumlahKaryawan());
            cabangAsli.setJamOperasional(cabangAsliDTO.getJamOperasional());
            try {
                Pengguna pengguna = userRepository.findByIdKaryawan(cabangAsliDTO.getIdKepalaOperasional());
                cabangAsli.setIdKepalaOperasional(pengguna.getIdKaryawan());
                cabangAsli.setCreatedAt(LocalDateTime.now());
                cabangAsli.setUpdatedAt(LocalDateTime.now());

                cabangAsliDb.save(cabangAsli);
                pengguna.setNomorCabang(cabangAsli.getNomorCabang());
            } catch (Exception e) {
                System.out.println("Kepala Operasional tidak ditemukan");
                return null;
            }

            return convertToDTO(cabangAsli);
        }

        @Override
        public CabangAsliResponseDTO updateCabangAsli(String id, CabangAsliDTO cabangAsliDTO) {
            CabangAsli cabangAsli = cabangAsliDb.findById(id)
                    .orElseThrow(() -> new NoSuchElementException("Cabang Asli dengan ID " + id + " tidak ditemukan"));

            if (cabangAsli.getDeletedAt() != null) {
                throw new NoSuchElementException("Cabang Asli dengan ID " + id + " sudah dihapus");
            }

            // Validasi Kepala Operasional
            validateKepalaOperasional(cabangAsliDTO.getIdKepalaOperasional());

            cabangAsli.setNamaCabang(cabangAsliDTO.getNamaCabang());
            cabangAsli.setAlamat(cabangAsliDTO.getAlamat());
            cabangAsli.setKontak(cabangAsliDTO.getKontak());
            cabangAsli.setJumlahKaryawan(cabangAsliDTO.getJumlahKaryawan());
            cabangAsli.setJamOperasional(cabangAsliDTO.getJamOperasional());
            cabangAsli.setIdKepalaOperasional(cabangAsliDTO.getIdKepalaOperasional());

            CabangAsli updatedCabangAsli = cabangAsliDb.save(cabangAsli);
            return convertToDTO(updatedCabangAsli);
        }

        private void validateKepalaOperasional(String idKepalaOperasional) {
            Pengguna user = userRepository.findById(idKepalaOperasional)
                    .orElseThrow(() -> new NoSuchElementException("User dengan ID " + idKepalaOperasional + " tidak ditemukan"));

            if (!"Kepala Operasional Cabang".equalsIgnoreCase(user.getRole())) {
                throw new IllegalArgumentException("User dengan ID " + idKepalaOperasional + " bukan Kepala Operasional Cabang");
            }
        }


        @Override
        public void deleteCabangAsli(String id) {
            CabangAsli cabangAsli = cabangAsliDb.findById(id)
                    .orElseThrow(() -> new NoSuchElementException("Cabang Asli dengan ID " + id + " tidak ditemukan"));

            cabangAsli.setDeletedAt(LocalDateTime.now());
            cabangAsliDb.save(cabangAsli);
        }

        // Helper method to convert CabangAsli entity to CabangAsliResponseDTO
        private CabangAsliResponseDTO convertToDTO(CabangAsli cabangAsli) {
            CabangAsliResponseDTO responseDTO = new CabangAsliResponseDTO();
            responseDTO.setNomorCabang(cabangAsli.getNomorCabang());
            responseDTO.setNamaCabang(cabangAsli.getNamaCabang());
            responseDTO.setAlamat(cabangAsli.getAlamat());
            responseDTO.setKontak(cabangAsli.getKontak());
            responseDTO.setJumlahKaryawan(cabangAsli.getJumlahKaryawan());
            responseDTO.setJamOperasional(cabangAsli.getJamOperasional());
            responseDTO.setIdKepalaOperasional(cabangAsli.getIdKepalaOperasional());
            responseDTO.setCreatedAt(cabangAsli.getCreatedAt());
            responseDTO.setUpdatedAt(cabangAsli.getUpdatedAt());
            return responseDTO;
        }

    // Helper method to generate fake CabangAsli data using JavaFaker
    public List<CabangAsli> generateFakeCabangAsli(int count) {
        List<CabangAsli> fakeCabangAsliList = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            CabangAsli cabangAsli = new CabangAsli();
            // Generate numeric string for nomorCabang
            String nomorCabang = faker.number().digits(6);
            cabangAsli.setNomorCabang(nomorCabang);
            cabangAsli.setNamaCabang("Cabang " + faker.address().cityName());
            cabangAsli.setAlamat(faker.address().fullAddress());
            cabangAsli.setKontak(faker.phoneNumber().phoneNumber());
            cabangAsli.setJumlahKaryawan(faker.number().numberBetween(5, 50));

            // Creating a Time object from long value (milliseconds since midnight)
            long millisInDay = faker.number().numberBetween(28800000, 64800000); // Between 8 AM and 6 PM
            cabangAsli.setJamOperasional(new Time(millisInDay));

            cabangAsli.setIdKepalaOperasional(faker.number().digits(6));

            fakeCabangAsliList.add(cabangAsli);
        }

        return cabangAsliDb.saveAll(fakeCabangAsliList);
    }
}