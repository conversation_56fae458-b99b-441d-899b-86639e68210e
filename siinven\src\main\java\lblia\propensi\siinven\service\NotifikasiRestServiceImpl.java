package lblia.propensi.siinven.service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lblia.propensi.siinven.dto.request.NotifikasiRequestDTO;
import lblia.propensi.siinven.dto.response.NotifikasiResponseDTO;
import lblia.propensi.siinven.model.Notifikasi;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.repository.NotifikasiDb;


@Service
public class NotifikasiRestServiceImpl implements NotifikasiRestService {
    @Autowired
    private NotifikasiDb notifikasiDb;
    
    @Override
public NotifikasiResponseDTO createNotifikasi(NotifikasiRequestDTO notifikasiRequestDTO) {
    
    if (notifikasiRequestDTO.getRolePengirim() == null || notifikasiRequestDTO.getRolePenerima() == null) {
        throw new IllegalArgumentException("Role pengirim dan penerima tidak boleh kosong");
    }
    
    validateRole(notifikasiRequestDTO.getRolePengirim());
    validateRole(notifikasiRequestDTO.getRolePenerima());
   
    Notifikasi notifikasi = new Notifikasi();
    notifikasi.setIdNotifikasi(UUID.randomUUID().toString());
    notifikasi.setRolePengirim(notifikasiRequestDTO.getRolePengirim());
    notifikasi.setRolePenerima(notifikasiRequestDTO.getRolePenerima());
    notifikasi.setIsiNotifikasi(notifikasiRequestDTO.getIsiNotifikasi());
    notifikasi.setNomorCabang(notifikasiRequestDTO.getNomorCabang());
    notifikasi.setIdPengajuan(notifikasiRequestDTO.getIdPengajuan());
    

     
    Notifikasi savedNotifikasi = notifikasiDb.save(notifikasi);
    
    return convertToResponseDTO(savedNotifikasi);
}

@Override
public void kirimNotifikasi(String rolePengirim, String rolePenerima, String nomorCabang, String isiNotifikasi, String idPengajuan) {
    if (rolePengirim == null || rolePenerima == null || isiNotifikasi == null) {
        throw new IllegalArgumentException("Role pengirim, penerima, dan isi notifikasi tidak boleh kosong");
    }
    
    validateRole(rolePengirim);
    validateRole(rolePenerima);
    
    Notifikasi notifikasi = new Notifikasi();
    notifikasi.setIdNotifikasi(UUID.randomUUID().toString());
    notifikasi.setRolePengirim(rolePengirim);
    notifikasi.setRolePenerima(rolePenerima);
    notifikasi.setNomorCabang(nomorCabang);
    notifikasi.setIsiNotifikasi(isiNotifikasi);
    notifikasi.setIdPengajuan(idPengajuan);
    
    notifikasiDb.save(notifikasi);
}

    @Override
    public Notifikasi simpanNotifikasi(Notifikasi notifikasi) {
        validateRole(notifikasi.getRolePengirim());
        validateRole(notifikasi.getRolePenerima());
        
        if (notifikasi.getIdNotifikasi() == null) {
            notifikasi.setIdNotifikasi(UUID.randomUUID().toString());
        }
        

        
        return notifikasiDb.save(notifikasi);
    }
    
    @Override
    public List<NotifikasiResponseDTO> getNotifikasiByRoleDanCabang(String rolePenerima, String nomorCabang) {
        validateRole(rolePenerima);
        List<Notifikasi> notifikasiList = notifikasiDb.findByRolePenerimaAndNomorCabang(rolePenerima, nomorCabang);
        return notifikasiList.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<NotifikasiResponseDTO> getNotifikasiByPengajuan(String idPengajuan) {
        List<Notifikasi> notifikasiList = notifikasiDb.findByIdPengajuan(idPengajuan);
        return notifikasiList.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }
    
    
    @Override
    public List<NotifikasiResponseDTO> getNotifikasiByRolePenerima(String rolePenerima) {
        validateRole(rolePenerima);
        List<Notifikasi> notifikasiList = notifikasiDb.findByRolePenerima(rolePenerima);
        return notifikasiList.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<NotifikasiResponseDTO> getNotifikasiByNomorCabang(String nomorCabang) {
        List<Notifikasi> notifikasiList = notifikasiDb.findByNomorCabang(nomorCabang);
        return notifikasiList.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<NotifikasiResponseDTO> getAllNotifikasi() {
        List<Notifikasi> notifikasiList = notifikasiDb.findAll();
        return notifikasiList.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }
    
    private NotifikasiResponseDTO convertToResponseDTO(Notifikasi notifikasi) {
        return new NotifikasiResponseDTO(
            notifikasi.getIdNotifikasi(),
            notifikasi.getRolePengirim(),
            notifikasi.getRolePenerima(),
            notifikasi.getNomorCabang(),
            notifikasi.getIsiNotifikasi(),
            notifikasi.getTanggalNotifikasi(),
            notifikasi.getIdPengajuan()
        );
    }


    private void validateRole(String role) {
        List<String> roles = Arrays.asList(
                "Staf Gudang Pelaksana Umum",
                "Staf Inventarisasi",
                "Staf Pengadaan dan Pembelian",
                "Kepala Departemen SDM dan Umum",
                "Direktur Utama",
                "Staf keuangan",
                "Admin",
                "Kepala Operasional Cabang"
        );

        if (role != null && !roles.contains(role)) {
            throw new IllegalArgumentException("Role tidak valid: " + role);
        }
    }
}