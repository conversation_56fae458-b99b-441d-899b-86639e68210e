package lblia.propensi.siinven.controller.authentication;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lblia.propensi.siinven.dto.request.authentication.GantiPasswordDTO;
import lblia.propensi.siinven.dto.request.authentication.PenggunaEditDTO;
import lblia.propensi.siinven.dto.request.authentication.PenggunaLoginDTO;
import lblia.propensi.siinven.dto.request.authentication.PenggunaRegisterDTO;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.NotifikasiResponseDTO;
import lblia.propensi.siinven.dto.response.authentication.PenggunaUsernameResponse;
import lblia.propensi.siinven.dto.response.authentication.PenggunaRegisterResponseDTO;
import lblia.propensi.siinven.dto.response.authentication.ProfilePenggunaResponseDTO;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.repository.PenggunaDb;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import lblia.propensi.siinven.service.UserRestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/api/auth")
@Tag(name = "Authentication Management", description = "APIs untuk menangani autentikasi pengguna")
public class AuthenticationController {
    private final JwtUtils jwtUtils;
    private final UserDetailsService userDetailsService;
    private final PenggunaDb penggunaDb;
    private final UserRestService userRestService;

    public AuthenticationController(JwtUtils jwtUtils,
                            UserDetailsService userDetailsService,
                            PenggunaDb penggunaDb,
                            UserRestService userRestService) {
        this.jwtUtils = jwtUtils;
        this.userDetailsService = userDetailsService;
        this.penggunaDb = penggunaDb;
        this.userRestService = userRestService;
    }

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationController.class);

    @PostMapping("/signup")
    @Operation(summary = "Register a new user", description = "Endpoint untuk mendaftar pengguna baru")
    public ResponseEntity<?> signUp(@RequestBody PenggunaRegisterDTO penggunaRegisterDTO) {
        BaseResponseDTO<PenggunaRegisterResponseDTO> response = new BaseResponseDTO<PenggunaRegisterResponseDTO>();

        String username = penggunaRegisterDTO.getUsername();
        String password = penggunaRegisterDTO.getPassword();
        String role = penggunaRegisterDTO.getRole();
        String id = penggunaRegisterDTO.getIdKaryawan();

        if(penggunaRegisterDTO.getNomorCabang() == null){
            penggunaRegisterDTO.setNomorCabang("0");
        }

        if(penggunaRegisterDTO.getNomorTelepon() == null){
            penggunaRegisterDTO.setNomorTelepon("0");
        }

        if(penggunaRegisterDTO.getNamaLengkap() == null){
            penggunaRegisterDTO.setNamaLengkap("0");
        }

        if (penggunaDb.findByUsername(username) != null) {
            response.setStatus(400);
            response.setMessage("Username already exists");
            return ResponseEntity.badRequest().body(response);
        }
        if (username == null || password == null || role == null || id == null) {
            response.setStatus(400);
            response.setMessage("Username, password, role, and id must be provided");
            return ResponseEntity.badRequest().body(response);
        }

        String message = userRestService.createUser(penggunaRegisterDTO);

        if (message.equals("User successfully created!")) {

            PenggunaRegisterResponseDTO penggunaRegisterResponseDTO = new PenggunaRegisterResponseDTO();
            penggunaRegisterResponseDTO.setMessage(message);

            response.setStatus(201);
            response.setMessage("User successfully created!");
            response.setTimestamp(new Date());
            response.setData(penggunaRegisterResponseDTO);

            return new ResponseEntity<>(response, HttpStatus.CREATED);
        } else {
            response.setStatus(400);
            response.setMessage("Failed to create user");
            response.setTimestamp(new Date());
            return ResponseEntity.badRequest().body(response);
        }


    }

    @PostMapping("/signin")
    @Operation(summary = "Login user", description = "Endpoint untuk login pengguna")
    public ResponseEntity<?> signIn(@RequestBody PenggunaLoginDTO penggunaLoginDTO) {
        try {
            BaseResponseDTO<HashMap<String, String>> response = new BaseResponseDTO<>();
            String username = penggunaLoginDTO.getUsername();
            String password = penggunaLoginDTO.getPassword();
            if (username == null || password == null) {
                response.setStatus(400);
                response.setMessage("Username and password must be provided");
                return ResponseEntity.badRequest().body(response);
            }
            if (penggunaDb.findByUsername(username) == null) {
                response.setStatus(400);
                response.setMessage("User not found");
                return ResponseEntity.badRequest().body(response);
            }
            ProfilePenggunaResponseDTO pengguna = userRestService.login(penggunaLoginDTO);
            if(pengguna == null) {
                response.setStatus(400);
                response.setMessage("Invalid username or password");
                return new ResponseEntity<>(response, HttpStatus.UNAUTHORIZED);
            }
            String token = jwtUtils.generateJwtToken(penggunaLoginDTO.getUsername());
            response.setStatus(200);
            response.setMessage("Login successful");
            response.setTimestamp(new Date());
            HashMap<String, String> data = new HashMap<>();
            data.put("accessToken", token);
            data.put("tokenType", "Bearer");
            response.setData(data);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(500);
            response.setMessage("Internal server error: " + e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/help")
    @Operation(summary = "Get help page", description = "Endpoint untuk mendapatkan cara penggunaan autentikasi")
    public ResponseEntity<?> getHowtoUse(@RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        String tokenWithoutBearer = token.substring(7);
        String username = jwtUtils.getUserNameFromJwtToken(tokenWithoutBearer);
        String role = jwtUtils.getRolesFromJWT(tokenWithoutBearer);
        Pengguna pengguna = userRestService.getUserByUsername(username);

        logger.info("User {} with role {} is accessing the help page", username, role);

        Map<String, Object> response = new HashMap<>();
        response.put("message", "This is the help page. You can find information about how to use the application here.");

        Map<String, String> data = new HashMap<>();
        data.put("username", username);
        data.put("role", role);
        response.put("data", data);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @GetMapping("/user")
    @Operation(summary = "Get user profile", description = "Endpoint untuk mendapatkan detail pengguna")
    public ResponseEntity<?> getUser(@RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        BaseResponseDTO<ProfilePenggunaResponseDTO> response = new BaseResponseDTO<>();

        String tokenWithoutBearer = token.substring(7);
        String username = jwtUtils.getUserNameFromJwtToken(tokenWithoutBearer);

        ProfilePenggunaResponseDTO profile = userRestService.getProfile(username);

        if (profile == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("User not found");
        }

        response.setStatus(200);
        response.setMessage("Mengembalikan detail pengguna");
        response.setData(profile);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PutMapping("/edit-user")
    @Operation(summary = "Edit user profile", description = "Endpoint untuk mengedit detail pengguna")
    public ResponseEntity<?> editUser(@RequestHeader("Authorization") String token,
                                       @RequestBody PenggunaEditDTO penggunaEditDTO) {
        if (token == null || !token.startsWith("Bearer ")) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        BaseResponseDTO<ProfilePenggunaResponseDTO> response = new BaseResponseDTO<>();

        String tokenWithoutBearer = token.substring(7);

        if(!jwtUtils.getRolesFromJWT(tokenWithoutBearer).equals("Admin")){
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body("Forbidden");
        }

        if (penggunaEditDTO.getUsername() == null) {
            response.setStatus(400);
            response.setMessage("Username and id must be provided");
            return ResponseEntity.badRequest().body(response);
        }

        ProfilePenggunaResponseDTO message = userRestService.updateUser(penggunaEditDTO);

        if (message != null) {
            response.setStatus(200);
            response.setMessage("Pengguna berhasil diperbarui");
            response.setData(message);
            response.setTimestamp(new Date());
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            response.setStatus(400);
            response.setMessage("Failed to update user");
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/edit-password")
    @Operation(summary = "Edit user password", description = "Endpoint untuk mengedit password pengguna")
    public ResponseEntity<?> editPassword(@RequestHeader("Authorization") String token,
                                          @RequestBody GantiPasswordDTO gantiPasswordDTO) {
        if (token == null || !token.startsWith("Bearer ")) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        logger.info("Received token: {}", token);

        BaseResponseDTO<ProfilePenggunaResponseDTO> response = new BaseResponseDTO<>();

        String tokenWithoutBearer = token.substring(7);
        String username = jwtUtils.getUserNameFromJwtToken(tokenWithoutBearer);

        logger.info("Extracted username from token: {}", username);

        if (gantiPasswordDTO.getNewPassword() == null) {
            response.setStatus(400);
            response.setMessage("Password lama dan baru harus diisi");
            return ResponseEntity.badRequest().body(response);
        }

        logger.info("Changing password for user: {}", username);

        ProfilePenggunaResponseDTO message = userRestService.changePassword(username, gantiPasswordDTO);

        logger.info("Password change result: {}", message);
        if (message != null) {
            logger.info("Password changed successfully for user: {}", username);
            response.setStatus(200);
            response.setMessage("Password berhasil diperbarui");
            response.setData(message);
            response.setTimestamp(new Date());
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            logger.error("Failed to change password for user: {}", username);
            response.setStatus(400);
            response.setMessage("Failed to update password");
            return ResponseEntity.badRequest().body(response);
        }


    }

    @GetMapping("/all-user")
    @Operation(summary = "Get all users", description = "Endpoint untuk mendapatkan semua pengguna")
    public ResponseEntity<?> getAllPengguna(@RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        String tokenWithoutBearer = token.substring(7);

        if(!jwtUtils.getRolesFromJWT(tokenWithoutBearer).equals("Admin")){
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body("Forbidden");
        }

        BaseResponseDTO<List<ProfilePenggunaResponseDTO>> response = new BaseResponseDTO<>();

        List<ProfilePenggunaResponseDTO> penggunaList = userRestService.getAllUser();

        if(penggunaList.isEmpty()) {
            response.setStatus(404);
            response.setMessage("No users found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } else {
            response.setStatus(200);
            response.setMessage("List of all users");
            response.setData(penggunaList);
            return new ResponseEntity<>(response, HttpStatus.OK);
        }

    }

    @GetMapping("/role")
    @Operation(summary = "Get user role from token", description = "Endpoint untuk mendapatkan role pengguna dari token")
    public ResponseEntity<?> getRoleFromToken(@RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        try {
            BaseResponseDTO<String> baseResponseDTO = new BaseResponseDTO<>();

            String tokenWithoutBearer = token.substring(7);
            String username = jwtUtils.getUserNameFromJwtToken(tokenWithoutBearer);

            if (username == null) {
                baseResponseDTO.setStatus(400);
                baseResponseDTO.setMessage("Username not found");
                return ResponseEntity.badRequest().body(baseResponseDTO);
            }

            String role = jwtUtils.getRolesFromJWT(tokenWithoutBearer);

            if (role == null) {
                baseResponseDTO.setStatus(400);
                baseResponseDTO.setMessage("Role not found");
                return ResponseEntity.badRequest().body(baseResponseDTO);
            }

            baseResponseDTO.setStatus(200);
            baseResponseDTO.setMessage("Role retrieved successfully");
            baseResponseDTO.setData(role);
            baseResponseDTO.setTimestamp(new Date());

            return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<String> baseResponseDTO = new BaseResponseDTO<>();
            baseResponseDTO.setStatus(500);
            baseResponseDTO.setMessage("Internal server error");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(baseResponseDTO);
        }


    }

    @GetMapping("/notifikasi")
    @Operation(summary = "Get user notifications", description = "Endpoint untuk mendapatkan notifikasi pengguna")
    public ResponseEntity<?> getNotifikasi(@RequestHeader("Authorization") String token) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }

            String tokenWithoutBearer = token.substring(7);
            Pengguna pengguna = userRestService.getUserByUsername(jwtUtils.getUserNameFromJwtToken(tokenWithoutBearer));

            if (pengguna == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("User not found");
            }

            List<NotifikasiResponseDTO> notifikasiList = userRestService.getNotifikasiByRoleDanCabang(pengguna.getRole(), pengguna.getNomorCabang());

            if(notifikasiList.isEmpty()) {
                BaseResponseDTO response = new BaseResponseDTO<>();
                response.setStatus(404);
                response.setMessage("No notifications found");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            BaseResponseDTO response = new BaseResponseDTO<>();

            response.setStatus(200);
            response.setMessage("Notifikasi berhasil diambil");
            response.setTimestamp(new Date());
            response.setData(notifikasiList);

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            BaseResponseDTO response = new BaseResponseDTO<>();
            response.setStatus(500);
            response.setMessage("Internal server error: " + e.getMessage());
            response.setTimestamp(new Date());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
