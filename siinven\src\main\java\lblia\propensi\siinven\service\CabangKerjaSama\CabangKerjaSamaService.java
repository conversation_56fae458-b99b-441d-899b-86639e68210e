package lblia.propensi.siinven.service.CabangKerjaSama;

import lblia.propensi.siinven.dto.request.CabangKerjaSama.CabangKerjaSamaRequestDTO;
import lblia.propensi.siinven.dto.response.CabangKerjaSama.CabangKerjaSamaResponseDTO;
import lblia.propensi.siinven.model.CabangKerjaSama;

import java.util.List;

public interface CabangKerjaSamaService {
    CabangKerjaSama findCabangKerjaSamaByNomorCabang(String nomorCabang);
    CabangKerjaSama findCabangKerjaSamaByNamaMitra(String namaMitra);
    void createCabangKerjaSama(CabangKerjaSamaRequestDTO cabangKerjaSamaDTO);
    CabangKerjaSamaResponseDTO getCabangKerjaSamaByNomorCabang(String nomorCabang) throws Exception;
    List<CabangKerjaSamaResponseDTO> getListCabangKerja();
    void updateCabangKerjaSama(CabangKerjaSamaRequestDTO cabangKerjaSamaDTO) throws Exception;
    void deleteCabangKerjaSama(CabangKerjaSama cabangKerjaSama);
}
