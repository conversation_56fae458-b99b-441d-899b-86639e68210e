package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="input_stok_barang_cabang")
public class InputStokBarangCabang {
    @Id
    private String idInputStokBarangCabang;

    @Column(name = "id_pengajuan", nullable = true)
    private String idPengajuan;

    @Column(name = "kode_barang", nullable = true)
    private String kodeBarang;

    @Column(name = "nama_barang", nullable = true)
    private String namaBarang;

    @Column(name = "stok_barang_pusat", nullable = true)
    private int stokBarangPusatSaatIni;

    @Column(name = "stok_barang_cabang", nullable = true)
    private int stokBarangCabangSaatIni;

    @Column(name = "stok_input", nullable = true)
    private int stokInput;

    @Column(name = "harga_barang", nullable = true)
    private Double hargaBarang;

    @PrePersist
    protected void onCreate() {
        if (idInputStokBarangCabang == null) {
            idInputStokBarangCabang = UUID.randomUUID().toString();
        }
    }
}
