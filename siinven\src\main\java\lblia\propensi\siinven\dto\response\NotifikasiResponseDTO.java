package lblia.propensi.siinven.dto.response;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NotifikasiResponseDTO {
    private String idNotifikasi;
    private String rolePengirim;
    private String rolePenerima;
    private String nomorCabang;
    private String isiNotifikasi;
    @JsonFormat(shape = JsonFormat.Shape.STRING, timezone="Asia/Jakarta")
    private Date tanggalNotifikasi;
    private String idPengajuan;
}