package lblia.propensi.siinven.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiwayatPengecekanDTO {
    private String idPengajuan;
    private LocalDateTime waktuPengecekan;
    private String nomorCabang;
    private String namaCabang;
    private Integer jumlahItem;
    private Integer jumlahMasalah;
    private String statusPengecekan;
    private String idPetugas;
    private String namaPetugas;
}