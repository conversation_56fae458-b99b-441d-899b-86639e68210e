package lblia.propensi.siinven.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lblia.propensi.siinven.dto.request.UpdateStatusPengecekanRequest;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.request.PengecekanStokDTO;
import lblia.propensi.siinven.dto.response.RiwayatPengecekanDTO;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import lblia.propensi.siinven.service.PengecekanStokService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import lblia.propensi.siinven.dto.request.UpdateStokAktualRequest;
import java.util.Date;
import java.util.List;
import java.util.Arrays;
import java.util.NoSuchElementException;


@RestController
@RequestMapping("/api/pengecekan-stok")
@SecurityRequirement(name = "bearerAuth")
public class PengecekanStokController {
    
    @Autowired
    private PengecekanStokService pengecekanStokService;
    
    @Autowired
    private JwtUtils jwtUtils;

    // Roles allowed to access getItemsForPengecekan
    private final List<String> rolesAllowedForInisiasi = Arrays.asList(
        "Kepala Departemen SDM dan Umum",
        "Admin",
        "Kepala Operasional Cabang"
    );

    // Roles allowed to access submitPengecekan
    private final List<String> rolesAllowedForSubmit = Arrays.asList(
        "Kepala Departemen SDM dan Umum",
        "Admin",
        "Kepala Operasional Cabang"
    );

    // Roles allowed to access getRiwayatPengecekan
    private final List<String> rolesAllowedForRiwayat = Arrays.asList(
        "Kepala Departemen SDM dan Umum", 
        "Direktur Utama",
        "Admin",
        "Kepala Operasional Cabang"

    );


    private final List<String> rolesAllowedForRiwayatByCabang = Arrays.asList(
        "Kepala Departemen SDM dan Umum", 
        "Direktur Utama", 
        "Admin"
    );

   
    private final List<String> rolesAllowedForDetail = Arrays.asList(
        "Kepala Departemen SDM dan Umum", 
        "Direktur Utama",
        "Admin"
    );

    // Roles allowed to access getDetailPengecekanItem
    private final List<String> rolesAllowedForDetailItem = Arrays.asList(
        "Kepala Departemen SDM dan Umum", 
        "Direktur Utama", 
        "Kepala Operasional Cabang",
        "Admin"
    );

    // Roles allowed to access updateStatusPengecekan
    private final List<String> rolesAllowedForUpdateStatus = Arrays.asList(
        "Kepala Departemen SDM dan Umum", 
        "Direktur Utama",
        "Kepala Operasional Cabang",
        "Admin"
    );

    // Roles allowed to access updateStokAktual
    private final List<String> rolesAllowedForUpdateStok = Arrays.asList(
        "Kepala Departemen SDM dan Umum",
        "Kepala Operasional Cabang",
        "Admin"
    );

    // Helper method to validate authorization
    private ResponseEntity<?> validateAuthorization(String token, List<String> allowedRoles) {
        if (token == null || token.trim().isEmpty() || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Authorization header is required and must start with 'Bearer '");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        try {
            String jwtToken = token.substring(7);
            if (!jwtUtils.validateJwtToken(jwtToken)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Invalid or expired token");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            String userRole = jwtUtils.getRolesFromJWT(jwtToken);
            if (!allowedRoles.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Access denied for role: " + userRole);
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            return null; // Authorization successful
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token validation failed: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
    }
    
    @GetMapping("/inisiasi/{nomorCabang}")
    @Operation(summary = "Get items for verification", description = "Get all items for stock verification process for a specific branch")
    @ApiResponse(responseCode = "200", description = "Successfully retrieved items", 
            content = @Content(schema = @Schema(implementation = PengecekanStokDTO.class)))
    public ResponseEntity<?> getItemsForPengecekan(
            @PathVariable String nomorCabang, 
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        ResponseEntity<?> authValidation = validateAuthorization(token, rolesAllowedForInisiasi);
        if (authValidation != null) {
            return authValidation;
        }

        try {
            List<PengecekanStokDTO> items = pengecekanStokService.getAllBarangForPengecekan(nomorCabang);
            
            BaseResponseDTO<List<PengecekanStokDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Daftar barang untuk pengecekan berhasil diambil");
            response.setTimestamp(new Date());
            response.setData(items);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @PostMapping("/submit/{nomorCabang}")
    public ResponseEntity<?> submitPengecekan(
            @RequestBody List<PengecekanStokDTO> pengecekanStokList,
            @PathVariable String nomorCabang,
            @RequestParam String idPetugas,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        ResponseEntity<?> authValidation = validateAuthorization(token, rolesAllowedForSubmit);
        if (authValidation != null) {
            return authValidation;
        }

        try {
            BaseResponseDTO<String> result = pengecekanStokService.submitPengecekanStok(pengecekanStokList, idPetugas, nomorCabang);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/riwayat")
    public ResponseEntity<?> getRiwayatPengecekan(
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        ResponseEntity<?> authValidation = validateAuthorization(token, rolesAllowedForRiwayat);
        if (authValidation != null) {
            return authValidation;
        }

        try {
            List<RiwayatPengecekanDTO> riwayatList = pengecekanStokService.getAllRiwayatPengecekan();
            
            BaseResponseDTO<List<RiwayatPengecekanDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Riwayat pengecekan stok berhasil diambil");
            response.setTimestamp(new Date());
            response.setData(riwayatList);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/riwayat/cabang/{nomorCabang}")
    public ResponseEntity<?> getRiwayatPengecekanByCabang(
            @PathVariable String nomorCabang, 
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        ResponseEntity<?> authValidation = validateAuthorization(token, rolesAllowedForRiwayatByCabang);
        if (authValidation != null) {
            return authValidation;
        }

        try {
            List<RiwayatPengecekanDTO> riwayatList = pengecekanStokService.getRiwayatPengecekanByCabang(nomorCabang);
            
            BaseResponseDTO<List<RiwayatPengecekanDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Riwayat pengecekan stok cabang berhasil diambil");
            response.setTimestamp(new Date());
            response.setData(riwayatList);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/detail/{idPengajuan}")
    public ResponseEntity<?> getDetailPengecekan(
            @PathVariable String idPengajuan, 
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        ResponseEntity<?> authValidation = validateAuthorization(token, rolesAllowedForDetail);
        if (authValidation != null) {
            return authValidation;
        }

        try {
            List<PengecekanStokDTO> detailList = pengecekanStokService.getDetailPengecekanById(idPengajuan);
            
            if (detailList.isEmpty()) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.NOT_FOUND.value());
                response.setMessage("Data pengecekan tidak ditemukan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            
            BaseResponseDTO<List<PengecekanStokDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Detail pengecekan stok berhasil diambil");
            response.setTimestamp(new Date());
            response.setData(detailList);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/detail/{idPengajuan}/{kodeBarang}")
    public ResponseEntity<?> getDetailPengecekanItem(
            @PathVariable String idPengajuan,
            @PathVariable String kodeBarang,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        ResponseEntity<?> authValidation = validateAuthorization(token, rolesAllowedForDetailItem);
        if (authValidation != null) {
            return authValidation;
        }

        try {
            PengecekanStokDTO detail = pengecekanStokService.getDetailPengecekanByKodeBarang(kodeBarang, idPengajuan);
            
            if (detail == null) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.NOT_FOUND.value());
                response.setMessage("Data pengecekan item tidak ditemukan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            
            BaseResponseDTO<PengecekanStokDTO> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Detail pengecekan item berhasil diambil");
            response.setTimestamp(new Date());
            response.setData(detail);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PutMapping("/update-status/{idPengajuan}")
    public ResponseEntity<?> updateStatusPengecekan(
            @PathVariable String idPengajuan,
            @RequestBody UpdateStatusPengecekanRequest request,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        ResponseEntity<?> authValidation = validateAuthorization(token, rolesAllowedForUpdateStatus);
        if (authValidation != null) {
            return authValidation;
        }

        try {
            BaseResponseDTO<String> result = pengecekanStokService.updateStatusPengecekan(idPengajuan, request.getStatusPengecekan());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PutMapping("/update-stok-aktual/{idPengajuan}/{kodeBarang}")
    public ResponseEntity<?> updateStokAktual(
            @PathVariable String idPengajuan,
            @PathVariable String kodeBarang,
            @RequestBody UpdateStokAktualRequest request,
            @RequestHeader(value = "Authorization", required = false) String token) {
        
        ResponseEntity<?> authValidation = validateAuthorization(token, rolesAllowedForUpdateStok);
        if (authValidation != null) {
            return authValidation;
        }

        try {
            PengecekanStokDTO updatedItem = pengecekanStokService.updateStokAktual(
                    idPengajuan, 
                    kodeBarang, 
                    request.getStokAktual(),
                    request.getCatatan());
            
            if (updatedItem == null) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.NOT_FOUND.value());
                response.setMessage("Data pengecekan item tidak ditemukan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            
            BaseResponseDTO<PengecekanStokDTO> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Stok aktual berhasil diperbarui");
            response.setTimestamp(new Date());
            response.setData(updatedItem);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}