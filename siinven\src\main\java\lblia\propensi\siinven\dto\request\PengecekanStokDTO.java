package lblia.propensi.siinven.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PengecekanStokDTO {
    private String kodeBarang;
    private String namaBarang;
    private String kategoriBarang;
    private Integer stokSistem;
    private Integer stokAktual;
    private Double hargaBarang;
    private String bentuk;
    private String lokasiBarang;
    private Boolean perluDiperiksa;
    private String catatan;
    private String statusPengecekan;
}