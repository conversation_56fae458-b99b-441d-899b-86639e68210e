package lblia.propensi.siinven.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EditStokBarangResponse {
    private Integer kodeBarang;
    private String namaBarang;
    private String nomorCabang;
    private Integer stokSebelum;
    private Integer stokSesudah;
    private String pesan;
    private Boolean sukses;
}