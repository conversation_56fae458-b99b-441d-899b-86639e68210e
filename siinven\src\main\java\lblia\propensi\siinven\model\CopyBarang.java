package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="copy_barang")
public class CopyBarang {
    @Id
    private String kodeBarang;

    @Column(name = "nama_barang", nullable = true)
    private String namaBarang;

    @Column(name = "kategori_barang", nullable = true)
    private String kategoriBarang;

    @Column(name="stok_barang", nullable = true)
    private int stokBarang;

    @Column(name = "harga_barang", nullable = true)
    private Double hargaBarang;

    @Column(name = "bentuk_barang", nullable = true)
    private String bentukBarang;

    @PrePersist
    protected void onCreate() {
        if (kodeBarang == null) {
            kodeBarang = UUID.randomUUID().toString();
        }
    }
}
