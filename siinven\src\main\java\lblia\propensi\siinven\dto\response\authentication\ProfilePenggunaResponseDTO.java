package lblia.propensi.siinven.dto.response.authentication;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProfilePenggunaResponseDTO {
    @NotBlank
    private String idKaryawan;

    @NotBlank
    private String username;

    private String namaLengkap;

    @Email
    private String email;

    @NotNull
    private String role;

    private String nomorTelepon;

    private String nomorCabang;

    private boolean isCabangAsli;
}
