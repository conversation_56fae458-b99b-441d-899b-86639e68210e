package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.pusat.InputStokBarangListRequest;
import lblia.propensi.siinven.dto.request.pusat.PersetujuanInputBarangPusatRequest;
import lblia.propensi.siinven.dto.request.RevisiPengadaanPusatDTO;
import lblia.propensi.siinven.dto.response.CopyBarangResponse;
import lblia.propensi.siinven.dto.response.pusat.InputStokBarangTotalResponse;

import java.util.HashMap;
import java.util.List;

public interface InputStokBarangService {
    List<CopyBarangResponse> getAllBarang();
    List<HashMap<String, String>> getAllPengajuan();
    Boolean persetujuanInputBarangPusat(PersetujuanInputBarangPusatRequest request, String role);
    InputStokBarangTotalResponse inputStokBarang(InputStokBarangListRequest listInputStokBarang);
    InputStokBarangTotalResponse getInputStokBarangById(String idPengajuan);
    InputStokBarangTotalResponse revisiInputStokBarang(RevisiPengadaanPusatDTO listInputStokBarang, String idPengajuan);
}
