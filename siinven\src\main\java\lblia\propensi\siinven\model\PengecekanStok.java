package lblia.propensi.siinven.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="pengecekan_stok")
public class PengecekanStok {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer idPengecekan;

    @Column(name = "id_pengajuan", nullable = false)
    private String idPengajuan;
    
    @Column(name = "kode_barang", nullable = false)
    private String kodeBarang;
    
    @Column(name = "stok_sistem", nullable = false)
    private Integer stokSistem;
    
    @Column(name = "stok_aktual", nullable = true)
    private Integer stokAktual;
    
    @Column(name = "nomor_cabang", nullable = false)
    private String nomorCabang;
    
    @Column(name = "lokasi_barang", nullable = true)
    private String lokasiBarang;
    
    @Column(name = "perlu_diperiksa", nullable = false)
    private Boolean perluDiperiksa;
    
    @Column(name = "catatan", columnDefinition = "TEXT", nullable = true)
    private String catatan;
    
    @Column(name = "status_pengecekan", nullable = false)
    private String statusPengecekan;
    
    @Column(name = "id_petugas", nullable = false)
    private String idPetugas;
    
    @CreationTimestamp
    @Column(name = "waktu_pengecekan", nullable = false, updatable = false)
    private LocalDateTime waktuPengecekan;
    
    @PrePersist
    protected void onCreate() {
        if (idPengajuan == null) {
            idPengajuan = "PCK-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        }
        if (perluDiperiksa == null) {
            perluDiperiksa = false;
        }
        if (statusPengecekan == null) {
            statusPengecekan = "PENDING";
        }
    }
}