package lblia.propensi.siinven.controller;

import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.LogBarangResponse;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import lblia.propensi.siinven.dto.request.BarangRequestDTO;
import lblia.propensi.siinven.dto.response.BarangResponseDTO;
import lblia.propensi.siinven.service.BarangService;

import lblia.propensi.siinven.service.LogStokBarangService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.persistence.EntityNotFoundException;

import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.NoSuchElementException;

@RestController
@RequestMapping("/api/barang")
public class BarangController {
    @Autowired
    private JwtUtils jwtUtils;

    private final BarangService barangService;
    private final LogStokBarangService logStokBarangService;

    public BarangController(BarangService barangService, LogStokBarangService logStokBarangService) {
        this.barangService = barangService;
        this.logStokBarangService = logStokBarangService;
    }

    private boolean isInvalidToken(String token) {
        return token == null || !token.startsWith("Bearer ");
    }

    private final List<String> rolesAllowedToAccessBarang = Arrays.asList(
        "Staf Gudang Pelaksana Umum",
        "Pelaksana Umum",
        "Kepala Operasional Cabang",
        "Kepala Departemen SDM dan Umum"
    );

    private boolean isRoleAllowedToAccessBarang(String role) {
        return rolesAllowedToAccessBarang.contains(role);
    }

    @GetMapping("/list")
    public ResponseEntity<?> listBarang(@RequestHeader("Authorization") String token) {
        var baseResponseDTO = new BaseResponseDTO<>();

        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
        if (!isRoleAllowedToAccessBarang(userRole)) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }

        List<BarangResponseDTO> barangList = barangService.getAllBarang();
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(barangList);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }

    @GetMapping("/detail/{id}")
    public ResponseEntity<?> detailBarangById(@RequestHeader("Authorization") String token, @PathVariable Integer id) {
        var baseResponseDTO = new BaseResponseDTO<>();

        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        try {

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
                baseResponseDTO.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
            }

            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            if (!isRoleAllowedToAccessBarang(userRole)) {
                baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
                baseResponseDTO.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
            }

            var barang = barangService.getBarangById(id);

            baseResponseDTO.setStatus(HttpStatus.OK.value());
            baseResponseDTO.setData(barang);
            baseResponseDTO.setMessage("Success");
            return ResponseEntity.status(HttpStatus.OK).body(baseResponseDTO);
        } catch (NoSuchElementException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(new BaseResponseDTO<>(HttpStatus.NOT_FOUND.value(), e.getMessage(), new Date(), null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new BaseResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Terjadi error pada server. Error: " + e.getMessage(), new Date(), null));
        }
    }

    @PostMapping("/create")
    public ResponseEntity<?> createBarang(@RequestHeader("Authorization") String token, @RequestBody BarangRequestDTO barangRequestDTO, BindingResult bindingResult) {
        var baseResponseDTO = new BaseResponseDTO<>();

        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        try {

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
                baseResponseDTO.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
            }

            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            if (!isRoleAllowedToAccessBarang(userRole)) {
                baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
                baseResponseDTO.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
            }

            if (bindingResult.hasFieldErrors()) {
                String errorMessages = bindingResult.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
                baseResponseDTO.setStatus(HttpStatus.BAD_REQUEST.value());
                baseResponseDTO.setMessage(errorMessages);
                return new ResponseEntity<>(baseResponseDTO, HttpStatus.BAD_REQUEST);
            }

            // Cek jika nama barang sudah ada
            if (barangService.findBarangByNamaBarang(barangRequestDTO.getNamaBarang()) != null) {
                baseResponseDTO.setStatus(HttpStatus.CONFLICT.value());
                baseResponseDTO.setMessage(String.format("Barang dengan nama barang %s sudah ada.", barangRequestDTO.getNamaBarang()));
                return ResponseEntity.status(HttpStatus.CONFLICT).body(baseResponseDTO);
            }

            barangService.createBarang(barangRequestDTO);
            baseResponseDTO.setStatus(HttpStatus.CREATED.value());
            baseResponseDTO.setMessage("Success");
            baseResponseDTO.setData(String.format("Barang berhasil dibuat"));
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.CREATED);

        } catch (EntityNotFoundException e) {
            baseResponseDTO.setStatus(HttpStatus.NOT_FOUND.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.NOT_FOUND);

        } catch (IllegalArgumentException e) {
            baseResponseDTO.setStatus(HttpStatus.BAD_REQUEST.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.BAD_REQUEST);

        } catch (IllegalStateException e) {
            baseResponseDTO.setStatus(HttpStatus.CONFLICT.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.CONFLICT);

        } catch (Exception e) {
            baseResponseDTO.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            baseResponseDTO.setMessage("Terjadi error pada server. Error: " + e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/update/{kodeBarang}")
    public ResponseEntity<?> updateBarang(@RequestHeader("Authorization") String token, @PathVariable("kodeBarang") Integer id, @RequestBody BarangRequestDTO barangRequestDTO, BindingResult bindingResult) {
        var baseResponseDTO = new BaseResponseDTO<>();

        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        try {

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
                baseResponseDTO.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
            }

            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            if (!isRoleAllowedToAccessBarang(userRole)) {
                baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
                baseResponseDTO.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
            }

            if (bindingResult.hasFieldErrors()) {
                String errorMessages = bindingResult.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
                baseResponseDTO.setStatus(HttpStatus.BAD_REQUEST.value());
                baseResponseDTO.setMessage(errorMessages);
                return new ResponseEntity<>(baseResponseDTO, HttpStatus.BAD_REQUEST);
            }

            // Cek jika nama barang sudah ada
            var cekNamaBarang = barangService.findBarangByNamaBarang(barangRequestDTO.getNamaBarang());
            if (cekNamaBarang != null && !cekNamaBarang.getKodeBarang().equals(id)) {
                baseResponseDTO.setStatus(HttpStatus.CONFLICT.value());
                baseResponseDTO.setMessage(String.format("Barang dengan nama barang %s sudah ada.", barangRequestDTO.getNamaBarang()));
                return ResponseEntity.status(HttpStatus.CONFLICT).body(baseResponseDTO);
            }

            barangService.updateBarang(id, barangRequestDTO);
            baseResponseDTO.setStatus(HttpStatus.OK.value());
            baseResponseDTO.setMessage("Success");
            baseResponseDTO.setData("Barang berhasil diperbarui.");
            return ResponseEntity.status(HttpStatus.OK).body(baseResponseDTO);
        } catch (NoSuchElementException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(new BaseResponseDTO<>(HttpStatus.NOT_FOUND.value(), e.getMessage(), new Date(), null));
        } catch (IllegalArgumentException e) {
            baseResponseDTO.setStatus(HttpStatus.BAD_REQUEST.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.BAD_REQUEST);

        } catch (IllegalStateException e) {
            baseResponseDTO.setStatus(HttpStatus.CONFLICT.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.CONFLICT);

        } catch (Exception e) {
            baseResponseDTO.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            baseResponseDTO.setMessage("Terjadi error pada server. Error: " + e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/delete/{kodeBarang}")
    public ResponseEntity<?> deleteBarang(@RequestHeader("Authorization") String token, @PathVariable("kodeBarang") Integer id) {
        var baseResponseDTO = new BaseResponseDTO<>();

        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        try {

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
                baseResponseDTO.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
            }

            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            if (!isRoleAllowedToAccessBarang(userRole)) {
                baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
                baseResponseDTO.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
            }

            barangService.deleteBarang(id);
            baseResponseDTO.setStatus(HttpStatus.OK.value());
            baseResponseDTO.setData(String.format("Barang berhasil dihapus."));
            baseResponseDTO.setMessage("Success");
            return ResponseEntity.status(HttpStatus.OK).body(baseResponseDTO);

        } catch (EntityNotFoundException e) {
            baseResponseDTO.setStatus(HttpStatus.NOT_FOUND.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.NOT_FOUND);
        } catch (NoSuchElementException e) {
            baseResponseDTO.setStatus(HttpStatus.NOT_FOUND.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            baseResponseDTO.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            baseResponseDTO.setMessage("Terjadi error pada server. Error: " + e.getMessage());
            baseResponseDTO.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(baseResponseDTO);
        }
    }

    @GetMapping("log/{kodeBarang}")
    public ResponseEntity<?> getLogStokBarang(@PathVariable("kodeBarang") Integer kodeBarang) {
        BaseResponseDTO baseResponseDTO = new BaseResponseDTO<>();

        try {
            List<LogBarangResponse> logStokBarang = logStokBarangService.getLogStokBarang(kodeBarang);
            baseResponseDTO.setStatus(HttpStatus.OK.value());
            baseResponseDTO.setData(logStokBarang);
            baseResponseDTO.setMessage("Success");
            return ResponseEntity.status(HttpStatus.OK).body(baseResponseDTO);
        } catch (Exception e) {
            baseResponseDTO.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            baseResponseDTO.setMessage("Terjadi error pada server. Error: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(baseResponseDTO);
        }

    }
}