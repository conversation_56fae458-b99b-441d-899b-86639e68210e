package lblia.propensi.siinven.service;

import jakarta.transaction.Transactional;
import lblia.propensi.siinven.dto.request.KurangStokBarangListRequest;
import lblia.propensi.siinven.dto.request.pusat.InputStokBarangRequest;
import lblia.propensi.siinven.dto.response.StokBarangResponseDTO;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.repository.StokBarangRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class KurangStokBarangServiceImpl implements KurangStokBarangService {

    private final StokBarangRepository stokBarangRepository;

    private final LogStokBarangService logStokBarangService;

    public KurangStokBarangServiceImpl(StokBarangRepository stokBarangRepository, LogStokBarangService logStokBarangService) {
        this.stokBarangRepository = stokBarangRepository;
        this.logStokBarangService = logStokBarangService;
    }

    @Override
    public Integer kurangiStokBarang(KurangStokBarangListRequest listInputStokBarang) {
        List<StokBarang> stokBarangList = GetStokBarangByNomorCabang(listInputStokBarang.getNomorCabang());
        try {
            for(InputStokBarangRequest inputStokBarangRequest: listInputStokBarang.getListInputBarang()) {
                Integer kodeBarang = inputStokBarangRequest.getKodeBarang();
                Integer jumlahKurang = inputStokBarangRequest.getStokInput();
                assert stokBarangList != null;
                StokBarang stokBarang = getStokBarangByKode(kodeBarang, stokBarangList);
                if(stokBarang != null) {
                    Integer stokAwal = stokBarang.getStokBarang();
                    if(stokAwal >= jumlahKurang) {
                        stokBarang.setStokBarang(stokAwal - jumlahKurang);
                        stokBarangRepository.save(stokBarang);
                        logStokBarangService.createLogStokBarang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang(), stokAwal, stokBarang.getStokBarang(), null, "PNSB");
                    } else {
                        System.out.println("Stok tidak cukup untuk barang dengan kode: " + kodeBarang);
                        return 400;
                    }
                } else {
                    System.out.println("Barang dengan kode: " + kodeBarang + " tidak ditemukan.");
                    return 404;
                }
            }
            return 200;
        } catch (Exception exception) {
            System.out.println("Error: " + exception.getMessage());
            return 500;
        }
    }

    @Override
    public List<StokBarangResponseDTO> getStokBarangByCabang(String nomorCabang) {
        List<StokBarang> stokBarangList = GetStokBarangByNomorCabang(nomorCabang);
        List<StokBarangResponseDTO> stokBarangResponseDTOList = new ArrayList<>();
        if(stokBarangList != null) {
            for(StokBarang stokBarang : stokBarangList) {
                StokBarangResponseDTO stokBarangResponseDTO = new StokBarangResponseDTO();
                stokBarangResponseDTO.setKodeBarang(stokBarang.getKodeBarang());
                stokBarangResponseDTO.setNamaBarang(stokBarang.getNamaBarang());
                stokBarangResponseDTO.setStokBarang(stokBarang.getStokBarang());
                stokBarangResponseDTO.setNomorCabang(stokBarang.getNomorCabang());
                stokBarangResponseDTO.setKategoriBarang(stokBarang.getKategoriBarang());
                stokBarangResponseDTO.setBentuk(stokBarang.getBentuk());
                stokBarangResponseDTO.setHargaBarang(stokBarang.getHargaBarang());
                stokBarangResponseDTOList.add(stokBarangResponseDTO);
            }
        }
        return stokBarangResponseDTOList;
    }

    private List<StokBarang> GetStokBarangByNomorCabang(String nomorCabang) {
        List<StokBarang> stokBarangList = stokBarangRepository.findAll();
        List<StokBarang> result = new ArrayList<>();
        for(StokBarang stokBarang : stokBarangList) {
            if(stokBarang.getNomorCabang().equals(nomorCabang)) {
                result.add(stokBarang);
            }
        }
        return result;
    }

    private StokBarang getStokBarangByKode(Integer kodeBarang, List<StokBarang> stokBarangList) {

        for(StokBarang stokBarang : stokBarangList) {
            if(stokBarang.getKodeBarang().equals(kodeBarang)) {
                return stokBarang;
            }
        }
        return null;
    }


}