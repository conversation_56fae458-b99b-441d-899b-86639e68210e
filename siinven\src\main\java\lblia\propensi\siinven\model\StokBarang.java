package lblia.propensi.siinven.model;

import java.util.Date;
import java.util.List;
import io.jsonwebtoken.lang.Arrays;
import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "stok_barang")
public class StokBarang {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer kodeStokBarang;

    @Column(name = "kode_barang", nullable = false)
    private Integer kodeBarang;

    @Column(name="nama_barang", nullable = false)
    private String namaBarang;

    @Column(name="kategori_barang",nullable = true)
    private String kategoriBarang;

    @Column(name = "harga_barang", nullable = false)
    private Double hargaBarang;

    @Column(name ="bentuk", nullable = true)
    private String bentuk;

    @Column(name="stok_barang",nullable = false)
    private Integer stokBarang;

    @Column(name = "nomor_cabang", nullable = true)
    private String nomorCabang;
}
