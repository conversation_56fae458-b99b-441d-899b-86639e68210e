package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "log_stok_barang")
public class LogStokBarang {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "kode_barang", nullable = false)
    private Integer kodeBarang;

    @Column(name = "nomor_cabang", nullable = false)
    private String nomorCabang;

    @Column(name = "stok_sebelum", nullable = false)
    private Integer stokSebelum;

    @Column(name = "stok_sesudah", nullable = false)
    private Integer stokSesudah;

    @Column(name = "id_pengajuan", nullable = true)
    private String idPengajuan;

    @Column(name = "tanggal", nullable = false)
    private LocalDate stockDate;

    @Column(name="keterangan", nullable = false)
    private String keterangan;
}
