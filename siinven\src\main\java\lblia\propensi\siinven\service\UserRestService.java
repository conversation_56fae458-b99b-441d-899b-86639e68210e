package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.authentication.GantiPasswordDTO;
import lblia.propensi.siinven.dto.request.authentication.PenggunaEditDTO;
import lblia.propensi.siinven.dto.request.authentication.PenggunaLoginDTO;
import lblia.propensi.siinven.dto.request.authentication.PenggunaRegisterDTO;
import lblia.propensi.siinven.dto.response.NotifikasiResponseDTO;
import lblia.propensi.siinven.dto.response.authentication.PenggunaUsernameResponse;
import lblia.propensi.siinven.dto.response.authentication.ProfilePenggunaResponseDTO;
import lblia.propensi.siinven.model.Pengguna;

import java.util.List;

public interface UserRestService {
    String hashPassword(String password);
    ProfilePenggunaResponseDTO login(PenggunaLoginDTO user);
    Pengguna getUserByUsername(String username);
    String createUser(PenggunaRegisterDTO user);
    ProfilePenggunaResponseDTO getProfile(String username);
    ProfilePenggunaResponseDTO updateUser(PenggunaEditDTO profile);
    Pengguna getUserByIdPengguna(String id);
    List<ProfilePenggunaResponseDTO> getAllUser();
    ProfilePenggunaResponseDTO changePassword(String username, GantiPasswordDTO gantiPasswordDTO);
    List<NotifikasiResponseDTO> getNotifikasiByRoleDanCabang(String rolePenerima, String nomorCabang);
}
