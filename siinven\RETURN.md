
# Return Barang API

Dokumentasi API untuk pengelolaan proses return barang.

---

## Alur (Flow) Proses Return

1. **Pengajuan Return** (status awal: `PENGAJUAN`)
2. **Approval** oleh Kepala Departemen SDM & Umum:
   - <PERSON><PERSON> (`DISETUJUI`) dan perlakuan = "Dikembalikan", status berubah menjadi `DIKIRIM`
   - <PERSON><PERSON> (`DITOLAK`), return tidak dilanjutkan
3. **Pengiriman Barang**
4. **Penerimaan Barang** (ubah status menjadi `DITERIMA`)
5. **Konfirmasi Jumlah Barang** (ubah status menjadi `SELESAI`)

---

## Role Akses

- **Kepala Operasional Cabang:** Mengajukan return
- **Kepala Departemen SDM & Umum:** Approval return
- **Staf Gudang:** Konfirmasi return

---

## 1. Membuat Pengajuan Return

**Endpoint:** `POST /api/return`

**URL:**  
`http://localhost:8080/api/return`

**Headers:**
- `Content-Type: application/json`
- `Authorization: Bearer [token JWT]`

**Body Request:**
```json
{
  "kodeBarang": 123,
  "stokInput": 5,
  "perlakuan": "Dikembalikan",
  "alasanReturn": "Barang rusak tidak dapat digunakan"
}
```

---

## 2. Melihat Semua Data Return

**Endpoint:** `GET /api/return`

**URL:**  
`http://localhost:8080/api/return`

**Headers:**
- `Authorization: Bearer [token JWT]`

---

## 3. Melihat Return Berdasarkan Status

**Endpoint:** `GET /api/return/status/{status}`

**Contoh URL:**
- Melihat return yang menunggu approval:  
  `http://localhost:8080/api/return/status/approval`
- Melihat return dengan status DIKIRIM:  
  `http://localhost:8080/api/return/status/DIKIRIM`

**Headers:**
- `Authorization: Bearer [token JWT]`

---

## 4. Melihat Detail Return

**Endpoint:** `GET /api/return/{idReturn}`

**Contoh URL:**  
`http://localhost:8080/api/return/RET-001`

**Headers:**
- `Authorization: Bearer [token JWT]`

---

## 5. Approval Return

**Endpoint:** `PUT /api/return/{idReturn}/approve`

**URL:**  
`http://localhost:8080/api/return/RET-001/approve`

**Headers:**
- `Content-Type: application/json`
- `Authorization: Bearer [token JWT]`

**Body Request:**
- Jika disetujui:
```json
{
  "statusApproval": "DISETUJUI"
}
```
- Jika ditolak:
```json
{
  "statusApproval": "DITOLAK"
}
```

---

## 6. Update Status Return

**Endpoint:** `PUT /api/return/{idReturn}/status/{newStatus}`

**Contoh URL:**  
`http://localhost:8080/api/return/RET-001/status/DITERIMA`

**Headers:**
- `Authorization: Bearer [token JWT]`

---

## 7. Konfirmasi Return

**Endpoint:** `PUT /api/return/{idReturn}/konfirmasi`

**Contoh URL:**  
`http://localhost:8080/api/return/RET-001/konfirmasi`

**Headers:**
- `Content-Type: application/json`
- `Authorization: Bearer [token JWT]`

**Body Request:**
```json
{
  "jumlahDikonfirmasi": 5
}
```

---

## Catatan Penting

- Pastikan **kode barang** sudah **terdaftar** di database sebelum mengajukan return.
- Setiap perubahan status mengikuti flow:
  - `PENGAJUAN` → `DISETUJUI` → `DIKIRIM` → `DITERIMA` → `SELESAI`
  - Jika `DITOLAK`, proses selesai.

- Status-status yang digunakan:
  - `PENGAJUAN`
  - `DISETUJUI`
  - `DITOLAK`
  - `DIKIRIM`
  - `DITERIMA`
  - `SELESAI`
