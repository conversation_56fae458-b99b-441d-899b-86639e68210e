package lblia.propensi.siinven.service;

import jakarta.transaction.Transactional;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.request.PengecekanStokDTO;
import lblia.propensi.siinven.dto.response.RiwayatPengecekanDTO;
import lblia.propensi.siinven.model.Barang;
import lblia.propensi.siinven.model.PengecekanStok;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.repository.BarangDb;
import lblia.propensi.siinven.repository.PengecekanStokDb;
import lblia.propensi.siinven.repository.PenggunaDb;
import lblia.propensi.siinven.repository.StokBarangRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class PengecekanStokServiceImpl implements PengecekanStokService {
    
    @Autowired
    private PengecekanStokDb pengecekanStokDb;
    
    @Autowired
    private BarangDb barangDb;
    
    @Autowired
    private StokBarangRepository stokBarangRepository;
    
    @Autowired
    private PenggunaDb penggunaDb;
    
 
    
    @Override
    public List<PengecekanStokDTO> getAllBarangForPengecekan(String nomorCabang) {
        // Use StokBarangRepository to get items for the specified branch
        List<StokBarang> stokBarangList = stokBarangRepository.findByNomorCabang(nomorCabang);
        List<PengecekanStokDTO> pengecekanStokList = new ArrayList<>();

        for (StokBarang stokBarang : stokBarangList) {
            PengecekanStokDTO pengecekanStok = new PengecekanStokDTO();
            pengecekanStok.setKodeBarang(stokBarang.getKodeBarang().toString());
            pengecekanStok.setNamaBarang(stokBarang.getNamaBarang());
            pengecekanStok.setKategoriBarang(stokBarang.getKategoriBarang());
            pengecekanStok.setHargaBarang(stokBarang.getHargaBarang());
            pengecekanStok.setBentuk(stokBarang.getBentuk());
            pengecekanStok.setPerluDiperiksa(false);
            pengecekanStok.setStokAktual(0); // Will be updated during verification
            pengecekanStok.setStatusPengecekan("PENDING");
            pengecekanStok.setStokSistem(stokBarang.getStokBarang());
            pengecekanStok.setLokasiBarang("Gudang " + nomorCabang);

            pengecekanStokList.add(pengecekanStok);
        }

        return pengecekanStokList;
    }

    @Override
    public BaseResponseDTO<String> submitPengecekanStok(List<PengecekanStokDTO> listHasilPengecekan, String idPetugas, String nomorCabang) {
        BaseResponseDTO<String> response = new BaseResponseDTO<>();
        
        try {
            String idPengajuan = "PCK-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
            LocalDateTime waktuPengecekan = LocalDateTime.now();
            
            for (PengecekanStokDTO hasil : listHasilPengecekan) {
                PengecekanStok pengecekanStok = new PengecekanStok();
                pengecekanStok.setIdPengajuan(idPengajuan);
                pengecekanStok.setKodeBarang(hasil.getKodeBarang());
                pengecekanStok.setStokSistem(hasil.getStokSistem());
                pengecekanStok.setStokAktual(hasil.getStokAktual());
                pengecekanStok.setNomorCabang(nomorCabang);
                pengecekanStok.setLokasiBarang(hasil.getLokasiBarang());
                
                // Check if there's a discrepancy between system and actual stock
                pengecekanStok.setPerluDiperiksa(!Objects.equals(hasil.getStokSistem(), hasil.getStokAktual()));
                pengecekanStok.setCatatan(hasil.getCatatan());
                pengecekanStok.setWaktuPengecekan(waktuPengecekan);
                pengecekanStok.setIdPetugas(idPetugas);
                
                // Set status based on whether there's a discrepancy
                if (pengecekanStok.getPerluDiperiksa()) {
                    pengecekanStok.setStatusPengecekan("NEEDS_ATTENTION");
                } else {
                    pengecekanStok.setStatusPengecekan("VERIFIED");
                }
                
                pengecekanStokDb.save(pengecekanStok);
            }
            
            response.setStatus(200);
            response.setMessage("Pengecekan stok berhasil disimpan");
            response.setTimestamp(new Date());
            response.setData(idPengajuan);
            
            return response;
            
        } catch (Exception e) {
            response.setStatus(500);
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return response;
        }
    }

    @Override
    public List<RiwayatPengecekanDTO> getAllRiwayatPengecekan() {
        // Get all unique verification IDs
        List<PengecekanStok> allPengecekan = pengecekanStokDb.findAll();
        
        // Group by idPengajuan to get unique verification records
        Map<String, List<PengecekanStok>> groupedByIdPengajuan = allPengecekan.stream()
                .collect(Collectors.groupingBy(PengecekanStok::getIdPengajuan));
        
        List<RiwayatPengecekanDTO> riwayatList = new ArrayList<>();
        
        for (Map.Entry<String, List<PengecekanStok>> entry : groupedByIdPengajuan.entrySet()) {
            String idPengajuan = entry.getKey();
            List<PengecekanStok> items = entry.getValue();
            
            if (!items.isEmpty()) {
                PengecekanStok sample = items.get(0);
                
                RiwayatPengecekanDTO riwayat = new RiwayatPengecekanDTO();
                riwayat.setIdPengajuan(idPengajuan);
                riwayat.setWaktuPengecekan(sample.getWaktuPengecekan());
                riwayat.setNomorCabang(sample.getNomorCabang());
                riwayat.setNamaCabang("Cabang " + sample.getNomorCabang()); // Replace with actual branch name if available
                riwayat.setJumlahItem(items.size());
                riwayat.setJumlahMasalah((int) items.stream().filter(PengecekanStok::getPerluDiperiksa).count());
                
                // Determine overall status
                if (items.stream().anyMatch(item -> "NEEDS_ATTENTION".equals(item.getStatusPengecekan()))) {
                    riwayat.setStatusPengecekan("NEEDS_ATTENTION");
                } else if (items.stream().allMatch(item -> "VERIFIED".equals(item.getStatusPengecekan()))) {
                    riwayat.setStatusPengecekan("VERIFIED");
                } else {
                    riwayat.setStatusPengecekan("PENDING");
                }
                
                // Get user information
                Pengguna petugas = penggunaDb.findByIdKaryawan(sample.getIdPetugas());
                if (petugas != null) {
                    riwayat.setIdPetugas(petugas.getIdKaryawan());
                    riwayat.setNamaPetugas(petugas.getNamaLengkap());
                } else {
                    riwayat.setIdPetugas(sample.getIdPetugas());
                    riwayat.setNamaPetugas("Unknown");
                }
                
                riwayatList.add(riwayat);
            }
        }
        
        // Sort by waktuPengecekan (newest first)
        riwayatList.sort(Comparator.comparing(RiwayatPengecekanDTO::getWaktuPengecekan).reversed());
        
        return riwayatList;
    }

    @Override
    public List<RiwayatPengecekanDTO> getRiwayatPengecekanByCabang(String nomorCabang) {
        // Filter all verifications by branch number
        List<PengecekanStok> pengecekanList = pengecekanStokDb.findByNomorCabang(nomorCabang);
        
        // Group by idPengajuan
        Map<String, List<PengecekanStok>> groupedByIdPengajuan = pengecekanList.stream()
                .collect(Collectors.groupingBy(PengecekanStok::getIdPengajuan));
        
        List<RiwayatPengecekanDTO> riwayatList = new ArrayList<>();
        
        for (Map.Entry<String, List<PengecekanStok>> entry : groupedByIdPengajuan.entrySet()) {
            String idPengajuan = entry.getKey();
            List<PengecekanStok> items = entry.getValue();
            
            if (!items.isEmpty()) {
                PengecekanStok sample = items.get(0);
                
                RiwayatPengecekanDTO riwayat = new RiwayatPengecekanDTO();
                riwayat.setIdPengajuan(idPengajuan);
                riwayat.setWaktuPengecekan(sample.getWaktuPengecekan());
                riwayat.setNomorCabang(nomorCabang);
                riwayat.setNamaCabang("Cabang " + nomorCabang); // Replace with actual branch name
                riwayat.setJumlahItem(items.size());
                riwayat.setJumlahMasalah((int) items.stream().filter(PengecekanStok::getPerluDiperiksa).count());
                
                // Determine overall status
                if (items.stream().anyMatch(item -> "NEEDS_ATTENTION".equals(item.getStatusPengecekan()))) {
                    riwayat.setStatusPengecekan("NEEDS_ATTENTION");
                } else if (items.stream().allMatch(item -> "VERIFIED".equals(item.getStatusPengecekan()))) {
                    riwayat.setStatusPengecekan("VERIFIED");
                } else {
                    riwayat.setStatusPengecekan("PENDING");
                }
                
                // Get user information
                Pengguna petugas = penggunaDb.findByIdKaryawan(sample.getIdPetugas());
                if (petugas != null) {
                    riwayat.setIdPetugas(petugas.getIdKaryawan());
                    riwayat.setNamaPetugas(petugas.getNamaLengkap());
                } else {
                    riwayat.setIdPetugas(sample.getIdPetugas());
                    riwayat.setNamaPetugas("Unknown");
                }
                
                riwayatList.add(riwayat);
            }
        }
        
        // Sort by waktuPengecekan (newest first)
        riwayatList.sort(Comparator.comparing(RiwayatPengecekanDTO::getWaktuPengecekan).reversed());
        
        return riwayatList;
    }

    @Override
    public List<PengecekanStokDTO> getDetailPengecekanById(String idPengajuan) {
        List<PengecekanStok> pengecekanItemList = pengecekanStokDb.findByIdPengajuan(idPengajuan);
        List<PengecekanStokDTO> detailList = new ArrayList<>();
        
        for (PengecekanStok item : pengecekanItemList) {
            PengecekanStokDTO detail = new PengecekanStokDTO();
            detail.setKodeBarang(item.getKodeBarang());
            
            // Get barang information
            Optional<Barang> barangOpt = barangDb.findById(Integer.valueOf(item.getKodeBarang()));
            if (barangOpt.isPresent()) {
                Barang barang = barangOpt.get();
                detail.setNamaBarang(barang.getNamaBarang());
                detail.setKategoriBarang(barang.getKategoriBarang());
                detail.setHargaBarang(barang.getHargaBarang());
                detail.setBentuk(barang.getBentuk());
            } else {
                detail.setNamaBarang("Unknown");
                detail.setKategoriBarang("Unknown");
                detail.setHargaBarang(0.0);
                detail.setBentuk("Unknown");
            }
            
            detail.setStokSistem(item.getStokSistem());
            detail.setStokAktual(item.getStokAktual());
            detail.setLokasiBarang(item.getLokasiBarang());
            detail.setPerluDiperiksa(item.getPerluDiperiksa());
            detail.setCatatan(item.getCatatan());
            detail.setStatusPengecekan(item.getStatusPengecekan());
            
            detailList.add(detail);
        }
        
        return detailList;
    }

    @Override
    public PengecekanStokDTO getDetailPengecekanByKodeBarang(String kodeBarang, String idPengajuan) {
        // Find the specific item in a verification process
        List<PengecekanStok> pengecekanItems = pengecekanStokDb.findByIdPengajuan(idPengajuan);
        PengecekanStok targetItem = null;
        
        for (PengecekanStok item : pengecekanItems) {
            if (item.getKodeBarang().equals(kodeBarang)) {
                targetItem = item;
                break;
            }
        }
        
        if (targetItem == null) {
            return null;
        }
        
        PengecekanStokDTO detail = new PengecekanStokDTO();
        detail.setKodeBarang(targetItem.getKodeBarang());
        
        // Get barang information
        Optional<Barang> barangOpt = barangDb.findById(Integer.valueOf(kodeBarang));
        if (barangOpt.isPresent()) {
            Barang barang = barangOpt.get();
            detail.setNamaBarang(barang.getNamaBarang());
            detail.setKategoriBarang(barang.getKategoriBarang());
            detail.setHargaBarang(barang.getHargaBarang());
            detail.setBentuk(barang.getBentuk());
        } else {
            detail.setNamaBarang("Unknown");
            detail.setKategoriBarang("Unknown");
            detail.setHargaBarang(0.0);
            detail.setBentuk("Unknown");
        }
        
        detail.setStokSistem(targetItem.getStokSistem());
        detail.setStokAktual(targetItem.getStokAktual());
        detail.setLokasiBarang(targetItem.getLokasiBarang());
        detail.setPerluDiperiksa(targetItem.getPerluDiperiksa());
        detail.setCatatan(targetItem.getCatatan());
        detail.setStatusPengecekan(targetItem.getStatusPengecekan());
        
        return detail;
    }

    @Override
    public BaseResponseDTO<String> updateStatusPengecekan(String idPengajuan, String statusPengecekan) {
        BaseResponseDTO<String> response = new BaseResponseDTO<>();
        
        try {
            List<PengecekanStok> pengecekanItems = pengecekanStokDb.findByIdPengajuan(idPengajuan);
            
            if (pengecekanItems.isEmpty()) {
                response.setStatus(404);
                response.setMessage("Data pengecekan tidak ditemukan");
                response.setTimestamp(new Date());
                return response;
            }
            
            // Update status for all items in this verification
            for (PengecekanStok item : pengecekanItems) {
                item.setStatusPengecekan(statusPengecekan);
                pengecekanStokDb.save(item);
            }
            
            response.setStatus(200);
            response.setMessage("Status pengecekan berhasil diperbarui");
            response.setTimestamp(new Date());
            response.setData(idPengajuan);
            
            return response;
            
        } catch (Exception e) {
            response.setStatus(500);
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return response;
        }
    }



@Override
public PengecekanStokDTO updateStokAktual(String idPengajuan, String kodeBarang, Integer stokAktual, String catatan) {
    // Find the specific item in the verification process
    List<PengecekanStok> pengecekanItems = pengecekanStokDb.findByIdPengajuan(idPengajuan);
    PengecekanStok targetItem = null;
    
    for (PengecekanStok item : pengecekanItems) {
        if (item.getKodeBarang().equals(kodeBarang)) {
            targetItem = item;
            break;
        }
    }
    
    if (targetItem == null) {
        return null;
    }
    
    // Update the actual stock and check if there's still a discrepancy
    targetItem.setStokAktual(stokAktual);
    targetItem.setPerluDiperiksa(!Objects.equals(targetItem.getStokSistem(), stokAktual));
    
    // Update notes if provided
    if (catatan != null && !catatan.isEmpty()) {
        targetItem.setCatatan(catatan);
    }
    
    // Update status based on whether there's still a discrepancy
    if (targetItem.getPerluDiperiksa()) {
        targetItem.setStatusPengecekan("NEEDS_ATTENTION");
    } else {
        targetItem.setStatusPengecekan("VERIFIED");
    }
    
    // Save the updated item
    pengecekanStokDb.save(targetItem);
    
    // Convert to DTO and return
    PengecekanStokDTO updatedDto = new PengecekanStokDTO();
    updatedDto.setKodeBarang(targetItem.getKodeBarang());
    
    // Get barang information
    Optional<Barang> barangOpt = barangDb.findById(Integer.valueOf(kodeBarang));
    if (barangOpt.isPresent()) {
        Barang barang = barangOpt.get();
        updatedDto.setNamaBarang(barang.getNamaBarang());
        updatedDto.setKategoriBarang(barang.getKategoriBarang());
        updatedDto.setHargaBarang(barang.getHargaBarang());
        updatedDto.setBentuk(barang.getBentuk());
    } else {
        updatedDto.setNamaBarang("Unknown");
        updatedDto.setKategoriBarang("Unknown");
        updatedDto.setHargaBarang(0.0);
        updatedDto.setBentuk("Unknown");
    }
    
    updatedDto.setStokSistem(targetItem.getStokSistem());
    updatedDto.setStokAktual(targetItem.getStokAktual());
    updatedDto.setLokasiBarang(targetItem.getLokasiBarang());
    updatedDto.setPerluDiperiksa(targetItem.getPerluDiperiksa());
    updatedDto.setCatatan(targetItem.getCatatan());
    updatedDto.setStatusPengecekan(targetItem.getStatusPengecekan());
    
    return updatedDto;
}

}