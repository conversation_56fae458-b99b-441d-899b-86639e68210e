package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="input_stok_barang_pusat")
public class InputStokBarangPusat {
    @Id
    private String idInputStokBarangPusat;

    @Column(name = "id_pengajuan", nullable = true)
    private String idPengajuan;

    @Column(name = "kode_barang", nullable = true)
    private String kodeBarang;

    @Column(name = "nama_barang", nullable = true)
    private String namaBarang;

    @Column(name = "stok_barang_pusat", nullable = true)
    private Integer stokBarangSaatIni;

    @Column(name = "stok_input", nullable = true)
    private Integer stokInput;

    @Column(name = "harga_barang", nullable = true)
    private Double hargaBarang;

    @Column(name = "tanggal_dibuat", nullable = true)
    private LocalDateTime tanggalDibuat;

    @PrePersist
    protected void onCreate() {
        if (idInputStokBarangPusat == null) {
            idInputStokBarangPusat = UUID.randomUUID().toString();
        }
        tanggalDibuat = LocalDateTime.now();
    }
}
