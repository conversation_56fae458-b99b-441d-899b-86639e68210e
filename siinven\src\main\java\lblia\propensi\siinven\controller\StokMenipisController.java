package lblia.propensi.siinven.controller;

import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.StokMenipisResponseDTO;
import lblia.propensi.siinven.service.StokMenipisService;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/api/stok-menipis")
public class StokMenipisController {

    @Autowired
    private StokMenipisService stokMenipisService;

    @Autowired
    private JwtUtils jwtUtils;

    // Hanya boleh diakses oleh role berikut
    private static final Set<String> ALLOWED_ROLES = Set.of(
        "Direktur Utama",
        "Kepala Departemen SDM dan Umum"
    );

    @GetMapping
    public ResponseEntity<BaseResponseDTO<?>> getStokMenipis(
        @RequestHeader("Authorization") String token,
        @RequestParam(required = false) String kategori,
        @RequestParam(required = false) String level
    ) {
        // Validasi Authorization Header
        if (token == null || !token.startsWith("Bearer ")) {
            return buildErrorResponse(HttpStatus.UNAUTHORIZED, "Token tidak valid");
        }

        // Validasi JWT Token
        String jwtToken = token.substring(7);
        if (!jwtUtils.validateJwtToken(jwtToken)) {
            return buildErrorResponse(HttpStatus.UNAUTHORIZED, "Token tidak terautentikasi");
        }

        // Validasi Role
        String userRole = jwtUtils.getRolesFromJWT(jwtToken);
        if (!ALLOWED_ROLES.contains(userRole)) {
            return buildErrorResponse(HttpStatus.FORBIDDEN, "Akses ditolak: Role tidak diizinkan");
        }

        try {
            List<StokMenipisResponseDTO> result;
            
            // Filter berdasarkan kategori jika ada parameter
            if (kategori != null && !kategori.isEmpty()) {
                result = stokMenipisService.filterByKategori(kategori);
            } 
            // Filter berdasarkan level keparahan (KRITIS/WARNING)
            else if (level != null && !level.isEmpty()) {
                if ("KRITIS".equalsIgnoreCase(level)) {
                    result = stokMenipisService.getStokKritis();
                } else if ("WARNING".equalsIgnoreCase(level)) {
                    result = stokMenipisService.getStokWarning();
                } else {
                    return buildErrorResponse(HttpStatus.BAD_REQUEST, "Parameter level tidak valid");
                }
            } 
            // Tampilkan semua stok menipis (KRITIS + WARNING)
            else {
                result = stokMenipisService.getAllStokMenipis();
            }

            return buildSuccessResponse(result);

        } catch (Exception e) {
            return buildErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR, 
                "Terjadi kesalahan server: " + e.getMessage()
            );
        }
    }

    @GetMapping("/kategori")
    public ResponseEntity<BaseResponseDTO<?>> getKategoriStokMenipis(
        @RequestHeader("Authorization") String token
    ) {
        // Validasi token dan role (sama seperti di atas)
        if (!validateTokenAndRole(token)) {
            return buildErrorResponse(HttpStatus.FORBIDDEN, "Akses ditolak");
        }

        try {
            List<String> kategori = stokMenipisService.getAllKategori();
            return buildSuccessResponse(kategori);
        } catch (Exception e) {
            return buildErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "Gagal mengambil daftar kategori: " + e.getMessage()
            );
        }
    }

    // ===== HELPER METHODS =====
    private boolean validateTokenAndRole(String token) {
        if (token == null || !token.startsWith("Bearer ")) return false;
        
        String jwtToken = token.substring(7);
        if (!jwtUtils.validateJwtToken(jwtToken)) return false;
        
        String userRole = jwtUtils.getRolesFromJWT(jwtToken);
        return ALLOWED_ROLES.contains(userRole);
    }

    private <T> ResponseEntity<BaseResponseDTO<?>> buildSuccessResponse(T data) {
        BaseResponseDTO<T> response = new BaseResponseDTO<>();
        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Sukses");
        response.setData(data);
        return ResponseEntity.ok(response);
    }

    private ResponseEntity<BaseResponseDTO<?>> buildErrorResponse(HttpStatus status, String message) {
        BaseResponseDTO<String> response = new BaseResponseDTO<>();
        response.setStatus(status.value());
        response.setMessage(message);
        return ResponseEntity.status(status).body(response);
    }
}