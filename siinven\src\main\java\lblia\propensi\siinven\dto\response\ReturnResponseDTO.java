package lblia.propensi.siinven.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReturnResponseDTO {
    
    private String idInputStokBarangReturn;
    private String idPengajuan;
    private Integer kodeBarang;
    private String namaBarang;
    private int stokBarangSaatIni;
    private int stokInput;
    private String perlakuan;
    private Double hargaBarang;
    private String alasanReturn;
    private String statusApproval;
    private Integer jumlahDikonfirmasi;
    private String statusRetur;
}