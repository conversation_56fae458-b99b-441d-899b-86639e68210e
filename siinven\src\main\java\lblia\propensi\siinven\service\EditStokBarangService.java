package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.EditStokBarangRequest;
import lblia.propensi.siinven.dto.response.EditStokBarangResponse;
import lblia.propensi.siinven.dto.response.StokBarangDetailDTO;
import lblia.propensi.siinven.model.StokBarang;

import java.util.List;

public interface EditStokBarangService {
    EditStokBarangResponse editStokBarang(EditStokBarangRequest request, String username);
    List<StokBarangDetailDTO> getStokBarangByCabang(String nomorCabang);
    StokBarang getStokBarangByKodeAndCabang(Integer kodeBarang, String nomorCabang);
}