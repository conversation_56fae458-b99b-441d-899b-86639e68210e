package lblia.propensi.siinven.repository;

import lblia.propensi.siinven.model.Return;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReturnDb extends JpaRepository<Return, String> {
    
    List<Return> findByStatusApproval(String statusApproval);
    
    List<Return> findByStatusRetur(String statusRetur);
    
    List<Return> findByIdPengajuan(String idPengajuan);
}