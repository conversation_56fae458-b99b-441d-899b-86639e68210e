package lblia.propensi.siinven.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


import lblia.propensi.siinven.model.PengecekanStok;

import java.util.List;

@Repository
public interface PengecekanStokDb extends JpaRepository<PengecekanStok, Integer> {
    List<PengecekanStok> findByNomorCabang(String nomorCabang);
    List<PengecekanStok> findByIdPengajuan(String idPengajuan);
    List<PengecekanStok> findByPerluDiperiksa(Boolean perluDiperiksa);
    PengecekanStok findByKodeBarang(String kodeBarang);
    List<PengecekanStok> findByStatusPengecekan(String statusPengecekan);
}