package lblia.propensi.siinven.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import lblia.propensi.siinven.dto.request.KurangStokBarangListRequest;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.StokBarangResponseDTO;
import lblia.propensi.siinven.service.KurangStokBarangService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/kurang-stok-cabang")
@Tag(name = "Pengurangan Stok Barang", description = "APIs untuk pengurangan stok barang")
public class PenguranganStokBarangController {
    private final KurangStokBarangService kurangStokBarangService;

    public PenguranganStokBarangController(KurangStokBarangService kurangStokBarangService) {
        this.kurangStokBarangService = kurangStokBarangService;
    }

    @GetMapping("/get/{nomorCabang}")
    @Operation(summary = "Get Stok Barang", description = "Mendapatkan stok barang terkait dengan nomor cabang")
    public ResponseEntity<?> getPenguranganStokBarang(@PathVariable("nomorCabang") String nomorCabang) {
        try {
            BaseResponseDTO response = new BaseResponseDTO();

            List<StokBarangResponseDTO> stokBarangList = kurangStokBarangService.getStokBarangByCabang(nomorCabang);

            if (stokBarangList.isEmpty()) {
                response.setStatus(404);
                response.setMessage("Stok barang tidak ditemukan");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            response.setStatus(200);
            response.setMessage("Stok barang ditemukan");
            response.setData(stokBarangList);

            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error: " + e.getMessage());
        }
    }

    @PostMapping("/kurangi")
    @Operation(summary = "Pengurangan Stok Barang", description = "APIs untuk mengurangi stok barang")
    public ResponseEntity<?> kurangiStokBarang(@RequestBody  KurangStokBarangListRequest listInputStokBarang) {
        try {
            BaseResponseDTO response = new BaseResponseDTO();

            Integer result = kurangStokBarangService.kurangiStokBarang(listInputStokBarang);

            if (result == 500) {
                response.setStatus(result);
                response.setMessage("Internal Server Error");
                return ResponseEntity.status(500).body(response);
            } else if (result == 400) {
                response.setStatus(result);
                response.setMessage("Bad Request");
                return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
            } else if (result == 404) {
                response.setStatus(result);
                response.setMessage("Not Found");
                return ResponseEntity.status(404).body(response);
            }
            response.setStatus(result);
            response.setMessage("Stok barang berhasil dikurangi");
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error: " + e.getMessage());
        }
    }
}
