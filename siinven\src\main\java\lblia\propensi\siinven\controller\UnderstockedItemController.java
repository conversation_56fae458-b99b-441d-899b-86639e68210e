package lblia.propensi.siinven.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.UnderstockedItemResponseDTO;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import lblia.propensi.siinven.service.UnderstockedItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/api/stok-understocked")
@Tag(name = "Understocked Items", description = "API for managing understocked items")
public class UnderstockedItemController {

    @Autowired
    private UnderstockedItemService understockedItemService;

    @Autowired
    private JwtUtils jwtUtils;

    // Hanya boleh diakses oleh role berikut
    private static final Set<String> ALLOWED_ROLES = Set.of(
        "Direktur Utama",
        "Kepala Departemen SDM dan Umum"
    );

    @GetMapping
    @Operation(summary = "Get understocked items", description = "Get items with their stock status with optional filtering")
    public ResponseEntity<BaseResponseDTO<?>> getUnderstockedItems(
        @RequestHeader("Authorization") String token,
        @RequestParam(required = false) String kategori,
        @RequestParam(required = false) String cabang
    ) {
        // Validasi Authorization Header
        if (token == null || !token.startsWith("Bearer ")) {
            return buildErrorResponse(HttpStatus.UNAUTHORIZED, "Token tidak valid");
        }

        // Validasi JWT Token
        String jwtToken = token.substring(7);
        if (!jwtUtils.validateJwtToken(jwtToken)) {
            return buildErrorResponse(HttpStatus.UNAUTHORIZED, "Token tidak terautentikasi");
        }

        // Validasi Role
        String userRole = jwtUtils.getRolesFromJWT(jwtToken);
        if (!ALLOWED_ROLES.contains(userRole)) {
            return buildErrorResponse(HttpStatus.FORBIDDEN, "Akses ditolak: Role tidak diizinkan");
        }

        try {
            List<UnderstockedItemResponseDTO> result;

            // Filter berdasarkan cabang dan kategori
            if (cabang != null && !cabang.isEmpty() && kategori != null && !kategori.isEmpty()) {
                result = understockedItemService.getUnderstockedItemsByCabangAndKategori(cabang, kategori);
            }
            // Filter berdasarkan cabang saja
            else if (cabang != null && !cabang.isEmpty()) {
                result = understockedItemService.getUnderstockedItemsByCabang(cabang);
            }
            // Filter berdasarkan kategori saja
            else if (kategori != null && !kategori.isEmpty()) {
                result = understockedItemService.getUnderstockedItemsByKategori(kategori);
            }
            // Tampilkan semua item understocked
            else {
                result = understockedItemService.getAllUnderstockedItems();
            }

            if (result.isEmpty()) {
                return buildErrorResponse(HttpStatus.NOT_FOUND, "Tidak ditemukan item dengan stok kurang");
            }

            return buildSuccessResponse(result);

        } catch (Exception e) {
            return buildErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "Terjadi kesalahan server: " + e.getMessage()
            );
        }
    }

    @GetMapping("/kategori")
    @Operation(summary = "Get all categories", description = "Get all categories for filtering")
    public ResponseEntity<BaseResponseDTO<?>> getAllKategori(
        @RequestHeader("Authorization") String token
    ) {
        // Validasi token dan role
        if (token == null || !token.startsWith("Bearer ")) {
            return buildErrorResponse(HttpStatus.UNAUTHORIZED, "Token tidak valid");
        }

        String jwtToken = token.substring(7);
        if (!jwtUtils.validateJwtToken(jwtToken)) {
            return buildErrorResponse(HttpStatus.UNAUTHORIZED, "Token tidak terautentikasi");
        }

        String userRole = jwtUtils.getRolesFromJWT(jwtToken);
        if (!ALLOWED_ROLES.contains(userRole)) {
            return buildErrorResponse(HttpStatus.FORBIDDEN, "Akses ditolak: Role tidak diizinkan");
        }

        try {
            // Get all categories from service
            List<String> kategoriList = understockedItemService.getAllKategori();
            return buildSuccessResponse(kategoriList);
        } catch (Exception e) {
            return buildErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "Gagal mengambil daftar kategori: " + e.getMessage()
            );
        }
    }

    @GetMapping("/cabang")
    @Operation(summary = "Get all branches", description = "Get all branches for filtering")
    public ResponseEntity<BaseResponseDTO<?>> getAllCabang(
        @RequestHeader("Authorization") String token
    ) {
        // Validasi token dan role
        if (token == null || !token.startsWith("Bearer ")) {
            return buildErrorResponse(HttpStatus.UNAUTHORIZED, "Token tidak valid");
        }

        String jwtToken = token.substring(7);
        if (!jwtUtils.validateJwtToken(jwtToken)) {
            return buildErrorResponse(HttpStatus.UNAUTHORIZED, "Token tidak terautentikasi");
        }

        String userRole = jwtUtils.getRolesFromJWT(jwtToken);
        if (!ALLOWED_ROLES.contains(userRole)) {
            return buildErrorResponse(HttpStatus.FORBIDDEN, "Akses ditolak: Role tidak diizinkan");
        }

        try {
            // Get all branches from service
            List<Map<String, String>> cabangList = understockedItemService.getAllCabang();
            return buildSuccessResponse(cabangList);
        } catch (Exception e) {
            return buildErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "Gagal mengambil daftar cabang: " + e.getMessage()
            );
        }
    }

    // ===== HELPER METHODS =====

    private <T> ResponseEntity<BaseResponseDTO<?>> buildSuccessResponse(T data) {
        BaseResponseDTO<T> response = new BaseResponseDTO<>();
        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Sukses");
        response.setData(data);
        return ResponseEntity.ok(response);
    }

    private ResponseEntity<BaseResponseDTO<?>> buildErrorResponse(HttpStatus status, String message) {
        BaseResponseDTO<String> response = new BaseResponseDTO<>();
        response.setStatus(status.value());
        response.setMessage(message);
        return ResponseEntity.status(status).body(response);
    }
}