package lblia.propensi.siinven.repository;

import lblia.propensi.siinven.model.InputStokBarangPusat;
import lblia.propensi.siinven.model.InputStokBarangPusatTotal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InputStokBarangPusatTotalDb extends JpaRepository<InputStokBarangPusatTotal, String> {
    // Custom query methods can be defined here if needed
}
