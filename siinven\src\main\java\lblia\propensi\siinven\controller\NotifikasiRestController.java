package lblia.propensi.siinven.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lblia.propensi.siinven.dto.request.NotifikasiRequestDTO;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.NotifikasiResponseDTO;
import lblia.propensi.siinven.service.NotifikasiRestService;

@RestController
@RequestMapping("/api/notifikasi")
public class NotifikasiRestController {

    @Autowired
    private NotifikasiRestService notifikasiRestService;

    @PostMapping("/kirim")
    public ResponseEntity<BaseResponseDTO<String>> kirimNotifikasi(
            @RequestParam String rolePengirim,
            @RequestParam String rolePenerima,
            @RequestParam(required = false) String nomorCabang,
            @RequestParam String isiNotifikasi,
            @RequestParam(required = false) String idPengajuan) {
        
        try {
            notifikasiRestService.kirimNotifikasi(rolePengirim, rolePenerima, nomorCabang, isiNotifikasi, idPengajuan);
            
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Notifikasi berhasil dikirim");
            response.setTimestamp(new Date());
            response.setData("Notifikasi berhasil disimpan ke database");

            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mengirim notifikasi: " + e.getMessage());
            response.setTimestamp(new Date());
 
            
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping
    public ResponseEntity<BaseResponseDTO<List<NotifikasiResponseDTO>>> getAllNotifikasi() {
        try {
            List<NotifikasiResponseDTO> notifikasiList = notifikasiRestService.getAllNotifikasi();
            
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Semua data notifikasi berhasil ditemukan");
            response.setTimestamp(new Date());
            response.setData(notifikasiList);
 
            
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mengambil semua data notifikasi: " + e.getMessage());
            response.setTimestamp(new Date());
    
            
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    @PostMapping
    public ResponseEntity<BaseResponseDTO<NotifikasiResponseDTO>> createNotifikasi(
            @RequestBody NotifikasiRequestDTO notifikasiRequestDTO) {
        
        try {
            NotifikasiResponseDTO savedNotifikasi = notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
            
            BaseResponseDTO<NotifikasiResponseDTO> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.CREATED.value());
            response.setMessage("Notifikasi berhasil dibuat");
            response.setTimestamp(new Date());
            response.setData(savedNotifikasi);
   
            
            return new ResponseEntity<>(response, HttpStatus.CREATED);
        } catch (Exception e) {
            BaseResponseDTO<NotifikasiResponseDTO> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal membuat notifikasi: " + e.getMessage());
            response.setTimestamp(new Date());

            
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/penerima/{rolePenerima}")
    public ResponseEntity<BaseResponseDTO<List<NotifikasiResponseDTO>>> getNotifikasiByRolePenerima(
            @PathVariable String rolePenerima) {
        
        try {
            List<NotifikasiResponseDTO> notifikasiList = notifikasiRestService.getNotifikasiByRolePenerima(rolePenerima);
            
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Data notifikasi berhasil ditemukan");
            response.setTimestamp(new Date());
            response.setData(notifikasiList);
   
            
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mengambil data notifikasi: " + e.getMessage());
            response.setTimestamp(new Date());
 
            
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/cabang/{nomorCabang}")
    public ResponseEntity<BaseResponseDTO<List<NotifikasiResponseDTO>>> getNotifikasiByNomorCabang(
            @PathVariable String nomorCabang) {
        
        try {
            List<NotifikasiResponseDTO> notifikasiList = notifikasiRestService.getNotifikasiByNomorCabang(nomorCabang);
            
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Data notifikasi berhasil ditemukan");
            response.setTimestamp(new Date());
            response.setData(notifikasiList);

            
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mengambil data notifikasi: " + e.getMessage());
            response.setTimestamp(new Date());

            
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    @GetMapping("/pengajuan/{idPengajuan}")
    public ResponseEntity<BaseResponseDTO<List<NotifikasiResponseDTO>>> getNotifikasiByPengajuan(
            @PathVariable String idPengajuan) {
        
        try {
            List<NotifikasiResponseDTO> notifikasiList = notifikasiRestService.getNotifikasiByPengajuan(idPengajuan);
            
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Data notifikasi berhasil ditemukan");
            response.setTimestamp(new Date());
            response.setData(notifikasiList);

            
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mengambil data notifikasi: " + e.getMessage());
            response.setTimestamp(new Date());

            
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    @GetMapping("/penerima/{rolePenerima}/cabang/{nomorCabang}")
    public ResponseEntity<BaseResponseDTO<List<NotifikasiResponseDTO>>> getNotifikasiByRoleDanCabang(
            @PathVariable String rolePenerima,
            @PathVariable String nomorCabang) {
        
        try {
            List<NotifikasiResponseDTO> notifikasiList = notifikasiRestService.getNotifikasiByRoleDanCabang(rolePenerima, nomorCabang);
            
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Data notifikasi berhasil ditemukan");
            response.setTimestamp(new Date());
            response.setData(notifikasiList);
            
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<List<NotifikasiResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mengambil data notifikasi: " + e.getMessage());
            response.setTimestamp(new Date());
            
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}