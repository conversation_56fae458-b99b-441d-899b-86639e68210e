package lblia.propensi.siinven.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReturnRequestDTO {
    
    private Integer kodeBarang;
    
    private String namaBarang;
    
    @NotNull(message = "Stok input harus diisi")
    @Positive(message = "Stok input harus lebih dari 0")
    private Integer stokInput;
    
    @NotBlank(message = "Perlakuan harus diisi")
    @Pattern(regexp = "Dibuang|Dikembalikan|Dijual|Disumbangkan", 
             message = "Perlakuan harus salah satu dari: Dibuang, Dikembalikan, Dijual, Disumbangkan")
    private String perlakuan;
    
    @NotBlank(message = "Alasan return harus diisi")
    private String alasanReturn;
    
    private String idPengajuan; // ID user yang mengajukan
}