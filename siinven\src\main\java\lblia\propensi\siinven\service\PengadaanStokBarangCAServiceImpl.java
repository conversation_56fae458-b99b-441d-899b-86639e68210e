package lblia.propensi.siinven.service;

import jakarta.transaction.Transactional;
import lblia.propensi.siinven.dto.request.NotifikasiRequestDTO;
import lblia.propensi.siinven.dto.request.cabang_asli.InputStokBarangCARequest;
import lblia.propensi.siinven.dto.request.cabang_asli.PersetujuanStokBarangCabangAsliRequest;
import lblia.propensi.siinven.dto.request.pusat.InputStokBarangRequest;
import lblia.propensi.siinven.dto.response.StokBarangResponseCabangDTO;
import lblia.propensi.siinven.dto.response.cabang_asli.PengadaanCabangAsliResponse;
import lblia.propensi.siinven.dto.response.cabang_asli.PengadaanCabangAsliTotalResponse;
import lblia.propensi.siinven.model.Barang;
import lblia.propensi.siinven.model.InputStokBarangCabang;
import lblia.propensi.siinven.model.InputStokBarangCabangTotal;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.repository.BarangDb;
import lblia.propensi.siinven.repository.InputStokBarangCabangDb;
import lblia.propensi.siinven.repository.InputStokBarangCabangTotalDb;
import lblia.propensi.siinven.repository.StokBarangRepository;

import org.springframework.beans.factory.annotation.Autowired;
import lblia.propensi.siinven.model.*;
import lblia.propensi.siinven.repository.*;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Transactional
public class PengadaanStokBarangCAServiceImpl implements PengadaanStokBarangCAService {
    private final BarangDb barangDb;
    private final StokBarangRepository stokBarangRepository;
    private final InputStokBarangCabangDb inputStokBarangCabangDb;
    private final InputStokBarangCabangTotalDb inputStokBarangCabangTotalDb;
    private final NotifikasiRestService notifikasiRestService;
    private final KeteranganPengajuanDb keteranganPengajuanDb;

    private final LogStokBarangService logStokBarangService;

    @Autowired
    private TrenPermintaanBukuService trenPermintaanBukuService;

    public PengadaanStokBarangCAServiceImpl(BarangDb barangDb, StokBarangRepository stokBarangRepository,
                                            InputStokBarangCabangDb inputStokBarangCabangDb,
                                            InputStokBarangCabangTotalDb inputStokBarangCabangTotalDb,
                                            NotifikasiRestService notifikasiRestService,
                                            KeteranganPengajuanDb keteranganPengajuanDb,
                                            LogStokBarangService logStokBarangService) {
        this.barangDb = barangDb;
        this.stokBarangRepository = stokBarangRepository;
        this.inputStokBarangCabangDb = inputStokBarangCabangDb;
        this.inputStokBarangCabangTotalDb = inputStokBarangCabangTotalDb;
        this.notifikasiRestService = notifikasiRestService;
        this.keteranganPengajuanDb = keteranganPengajuanDb;
        this.logStokBarangService = logStokBarangService;
    }

    @Override
    public List<StokBarangResponseCabangDTO> getAllBarang(String nomorCabang) {
        List<Barang> barangList = barangDb.findAll();
        List<StokBarangResponseCabangDTO> copyBarangResponseList = new ArrayList<>();
        List<StokBarang> stokBarangList = getStokBarangByNomorCabang(nomorCabang);
        List<StokBarang> stokBarangPusat = getStokBarangByNomorCabang("001");
        System.out.println(stokBarangList);
        for (Barang barang : barangList) {
            StokBarangResponseCabangDTO copyBarangResponse = new StokBarangResponseCabangDTO();
            copyBarangResponse.setKodeBarang(barang.getKodeBarang());
            copyBarangResponse.setNamaBarang(barang.getNamaBarang());
            copyBarangResponse.setHargaBarang(barang.getHargaBarang());
            System.out.println("Barang: " + barang.getKodeBarang());
            for (StokBarang stokBarang : stokBarangList) {
                System.out.println("Stok Barang: " + stokBarang.getKodeBarang() + "Barang: " + barang.getKodeBarang());
                if (Objects.equals(stokBarang.getKodeBarang(), barang.getKodeBarang())) {
                    System.out.println(stokBarang.getKodeBarang());
                    copyBarangResponse.setStokBarang(stokBarang.getStokBarang());
                }
            }
            if (copyBarangResponse.getStokBarang() == null) {
                copyBarangResponse.setStokBarang(0);
            }
            for (StokBarang stokBarang : stokBarangPusat) {
                if (Objects.equals(stokBarang.getKodeBarang(), barang.getKodeBarang())) {
                    copyBarangResponse.setStokBarangPusat(stokBarang.getStokBarang());
                }
            }
            if (copyBarangResponse.getStokBarangPusat() == null) {
                copyBarangResponse.setStokBarangPusat(0);
            }

            copyBarangResponseList.add(copyBarangResponse);
        }

        return copyBarangResponseList;

    }

    @Override
    public List<HashMap<String, String>> getAllPengajuan() {
        List<InputStokBarangCabangTotal> inputStokBarangPusatList = inputStokBarangCabangTotalDb.findAll();
        List<HashMap<String, String>> pengajuanList = new ArrayList<>();
        inputStokBarangPusatList.sort(Comparator.comparing(InputStokBarangCabangTotal::getWaktuPengajuan).reversed());
        for (InputStokBarangCabangTotal inputStokBarangPusat : inputStokBarangPusatList) {
            HashMap<String, String> pengajuan = new HashMap<>();
            pengajuan.put("idPengajuan", String.valueOf(inputStokBarangPusat.getIdPengajuan()));
            stepHandler(pengajuanList, inputStokBarangPusat, pengajuan);
        }
        return pengajuanList;
    }

    private void stepHandler(List<HashMap<String, String>> pengajuanList, InputStokBarangCabangTotal inputStokBarangPusat, HashMap<String, String> pengajuan) {
        if (inputStokBarangPusat.getStep() == 1 || inputStokBarangPusat.getStep() == 2) {
            pengajuan.put("step", String.valueOf(pendingHandler(inputStokBarangPusat.getIdPengajuan())));
        } else {
            pengajuan.put("step", String.valueOf(inputStokBarangPusat.getStep()));
        }
        pengajuan.put("nomorCabangTujuan", inputStokBarangPusat.getNomorCabangTujuan());
        pengajuan.put("nomorCabangAsal", inputStokBarangPusat.getNomorCabangAsal());

        pengajuan.put("waktuPengajuan", inputStokBarangPusat.getWaktuPengajuan().toString());
        pengajuanList.add(pengajuan);
    }

    @Override
    public List<HashMap<String, String>> getPengajuanByCabang(String nomorCabang) {
        List<InputStokBarangCabangTotal> inputStokBarangPusatList = inputStokBarangCabangTotalDb.findAll();
        List<HashMap<String, String>> pengajuanList = new ArrayList<>();
        inputStokBarangPusatList.sort(Comparator.comparing(InputStokBarangCabangTotal::getWaktuPengajuan).reversed());
        for (InputStokBarangCabangTotal inputStokBarangPusat : inputStokBarangPusatList) {
            HashMap<String, String> pengajuan = new HashMap<>();
            if (inputStokBarangPusat.getNomorCabangTujuan().equals(nomorCabang)) {
                pengajuan.put("idPengajuan", String.valueOf(inputStokBarangPusat.getIdPengajuan()));
                stepHandler(pengajuanList, inputStokBarangPusat, pengajuan);
            }
        }
        return pengajuanList;
    }

    @Override
    public Boolean persetujuanInputBarangCabangAsli(PersetujuanStokBarangCabangAsliRequest request, String role) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = inputStokBarangCabangTotalDb.findById(request.getIdPengajuan()).orElse(null);
            if (request.getKeterangan() != null) {
                System.out.println("Masuk keterangan");
                if (!request.getKeterangan().isEmpty()) {
                    buatKeterangan(request.getIdPengajuan(), request.getKeterangan(), role);
                }
            }
            if (inputStokBarangCabangTotal == null) {
                System.out.println("Pengajuan tidak ditemukan");
                return false;
            }
            if (inputStokBarangCabangTotal.getStep() == 1 && role.equals("Kepala Departemen SDM dan Umum")) {
                if (request.getStatus()) {
                    inputStokBarangCabangTotal.setStep(3);
                    inputStokBarangCabangTotal.setTotalHarga(inputStokBarangCabangTotal.getTotalHarga());
                    inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Kepala Departemen SDM dan Umum");
                    notifikasiRequestDTO.setRolePenerima("Staf Gudang Pelaksana Umum");
                    notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang oleh Kepala Departemen SDM telah disetujui");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
                    return true;
                } else {
                    inputStokBarangCabangTotal.setStep(0);
                    inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Kepala Departemen SDM dan Umum");
                    notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                    notifikasiRequestDTO.setRolePenerima("Kepala Operasional Cabang");
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang Cabang Asli ditolak");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);

                    return false;
                }
            } else if (inputStokBarangCabangTotal.getStep() == 3 && role.equals("Staf Gudang Pelaksana Umum")) {
                if (request.getStatus()) {
                    inputStokBarangCabangTotal.setStep(4);
                    inputStokBarangCabangTotal.setTotalHarga(inputStokBarangCabangTotal.getTotalHarga());
                    inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Staf Gudang Pelaksana Umum");
                    notifikasiRequestDTO.setRolePenerima("Kepala Operasional Cabang");
                    notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang telah disetujui, silahkan dibuat surat order");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
                    return true;
                }
            } else if (inputStokBarangCabangTotal.getStep() == 4 && role.equals("Kepala Operasional Cabang")) {
                if (request.getStatus()) {
                    boolean result = updateStokCabang(inputStokBarangCabangTotal.getIdPengajuan());
                    if (result) {
                        inputStokBarangCabangTotal.setStep(5);
                        inputStokBarangCabangTotal.setTotalHarga(inputStokBarangCabangTotal.getTotalHarga());
                        inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                        NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                        notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                        notifikasiRequestDTO.setRolePengirim("Kepala Operasional Cabang");
                        notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                        notifikasiRequestDTO.setRolePenerima("Staf Gudang Pelaksana Umum");
                        notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang telah disetujui");
                        notifikasiRestService.createNotifikasi(notifikasiRequestDTO);

                        trenPermintaanBukuService.initializePermintaanBuku(request.getNomorCabang(), inputStokBarangCabangTotal.getIdPengajuan());
                        return true;
                    } else {
                        System.out.println("Stok Barang Pusat tidak ditemukan di Persetujuan");
                        return false;
                    }
                }
            } else {
                System.out.println("Role tidak sesuai atau butuh keterangan");
                return false;
            }
        } catch (Exception exception) {
            System.out.println("Error: " + exception.getMessage());
            return false;
        }
        return false;
    }


    public void buatKeterangan(String idPengajuan, String keterangan, String role) {
        try {
            KeteranganPengajuan keteranganPengajuan = new KeteranganPengajuan();
            System.out.println(keterangan);
            keteranganPengajuan.setIdPengajuan(idPengajuan);
            keteranganPengajuan.setKeterangan(keterangan);
            keteranganPengajuan.setRolePengirim(role);
            keteranganPengajuanDb.save(keteranganPengajuan);
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
    }

    @Override
    public PengadaanCabangAsliTotalResponse inputStokBarangCabang(InputStokBarangCARequest listInputStokBarang) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = new InputStokBarangCabangTotal();
            List<PengadaanCabangAsliResponse> pengadaanCabangAsliResponseList = new ArrayList<>();
            inputStokBarangCabangTotal.setIdPengajuan(generateIdPengajuan(listInputStokBarang.getNomorCabang()));
            inputStokBarangCabangTotal.setNomorCabangAsal("001");
            inputStokBarangCabangTotal.setNomorCabangTujuan(listInputStokBarang.getNomorCabang());
            inputStokBarangCabangTotal.setStep(1);
            inputStokBarangCabangTotal.setWaktuPengajuan(LocalDateTime.now());
            inputStokBarangCabangTotal.setFlagCabang(true);

            double totalHarga = 0L;

            inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

            for (InputStokBarangRequest listInputStokBarangRequest : listInputStokBarang.getListInputBarang()) {
                Integer kodeBarang = listInputStokBarangRequest.getKodeBarang();
                Integer jumlahInput = listInputStokBarangRequest.getStokInput();
                Integer stokBarangPusatSaatIni;

                List<StokBarang> stokBarangList = getStokBarangByNomorCabang(listInputStokBarang.getNomorCabang());
                System.out.println("Kode Barang: " + kodeBarang + "Jumlah Input: " + jumlahInput);
                List<StokBarang> stokBarangPusatList = getStokBarangByNomorCabang("001");

                StokBarang stokBarang = getStokBarangByKodeBarang(kodeBarang, stokBarangList);
                StokBarang stokBarangPusat = getStokBarangByKodeBarang(kodeBarang, stokBarangPusatList);
                if(stokBarangPusat != null) {
                    stokBarangPusatSaatIni = stokBarangPusat.getStokBarang();
                } else {
                    stokBarangPusatSaatIni = 0;
                }

                if (stokBarang != null) {
                    System.out.println("Stok Barang: " + stokBarang.getKodeBarang());
                    InputStokBarangCabang inputStokBarangCabang = new InputStokBarangCabang();
                    inputStokBarangCabang.setStokBarangPusatSaatIni(stokBarangPusatSaatIni);
                    inputStokBarangCabang.setStokBarangCabangSaatIni(stokBarang.getStokBarang());
                    inputStokBarangCabang.setStokInput(jumlahInput);
                    inputStokBarangCabang.setHargaBarang(stokBarang.getHargaBarang());
                    inputStokBarangCabang.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    inputStokBarangCabang.setNamaBarang(stokBarang.getNamaBarang());
                    inputStokBarangCabang.setKodeBarang(String.valueOf(stokBarang.getKodeBarang()));
                    try {
                        inputStokBarangCabangDb.save(inputStokBarangCabang);
                    } catch (Exception e) {
                        e.printStackTrace();
                        System.out.println("Error: " + e.getMessage());
                    }
                    pengadaanCabangAsliResponseList.add(mapToInputStokBarangResponse(inputStokBarangCabang));
                    totalHarga += stokBarang.getHargaBarang() * jumlahInput;
                } else {
                    Barang barang = getBarangByKodeBarang(kodeBarang);
                    if (barang == null) {
                        System.out.println("Barang tidak ditemukan");
                        return null;
                    }
                    InputStokBarangCabang inputStokBarangCabang = new InputStokBarangCabang();
                    inputStokBarangCabang.setStokBarangCabangSaatIni(0);
                    inputStokBarangCabang.setStokBarangPusatSaatIni(stokBarangPusatSaatIni);
                    inputStokBarangCabang.setStokInput(jumlahInput);
                    inputStokBarangCabang.setHargaBarang(barang.getHargaBarang());
                    inputStokBarangCabang.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    inputStokBarangCabang.setNamaBarang(barang.getNamaBarang());
                    inputStokBarangCabang.setKodeBarang(String.valueOf(barang.getKodeBarang()));
                    try {
                        inputStokBarangCabangDb.save(inputStokBarangCabang);
                    } catch (Exception e) {
                        e.printStackTrace();
                        System.out.println("Error: " + e.getMessage());
                    }
                    pengadaanCabangAsliResponseList.add(mapToInputStokBarangResponse(inputStokBarangCabang));
                    totalHarga += barang.getHargaBarang() * jumlahInput;
                }
            }

            inputStokBarangCabangTotal.setTotalHarga(totalHarga);
            inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

            NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
            notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
            notifikasiRequestDTO.setRolePengirim("Kepala Operasional Cabang");
            notifikasiRequestDTO.setNomorCabang(listInputStokBarang.getNomorCabang());
            notifikasiRequestDTO.setRolePenerima("Kepala Departemen SDM dan Umum");
            notifikasiRequestDTO.setIsiNotifikasi("Pengajuan Stok Barang Cabang Asli");
            notifikasiRestService.createNotifikasi(notifikasiRequestDTO);

            PengadaanCabangAsliTotalResponse pengadaanCabangAsliTotalResponse = new PengadaanCabangAsliTotalResponse();

            pengadaanCabangAsliTotalResponse.setListInputStokBarang(pengadaanCabangAsliResponseList);
            pengadaanCabangAsliTotalResponse.setNomorCabang(listInputStokBarang.getNomorCabang());
            pengadaanCabangAsliTotalResponse.setIdPengajuan(String.valueOf(inputStokBarangCabangTotal.getIdPengajuan()));
            pengadaanCabangAsliTotalResponse.setTotalHarga(String.valueOf(totalHarga));
            pengadaanCabangAsliTotalResponse.setFlag(inputStokBarangCabangTotal.getFlagCabang());
            pengadaanCabangAsliTotalResponse.setStep(inputStokBarangCabangTotal.getStep());

            return pengadaanCabangAsliTotalResponse;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String generateIdPengajuan( String nomorCabangTujuan) {

        if (nomorCabangTujuan == null || nomorCabangTujuan.trim().isEmpty()) {
            throw new IllegalArgumentException("Nomor Cabang Tujuan tidak boleh kosong.");
        }

        int jumlahPengajuan = inputStokBarangCabangTotalDb.findAll().size() + 1;
        String tipeCabangAsal = "AS";


        if (jumlahPengajuan < 0 || jumlahPengajuan > 9999) {
            throw new IllegalArgumentException("Jumlah Pengajuan harus antara 0 dan 9999.");
        }

        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("PGJ");

        ZoneId zoneId = ZoneId.of("Asia/Jakarta");
        Date tanggalPengajuan = new Date();

        // Ubah Date ke LocalDate dengan zona Jakarta
        LocalDate localDate = tanggalPengajuan.toInstant()
                .atZone(zoneId)
                .toLocalDate();

        // Format menjadi YYYYMMDD
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        stringBuilder.append(localDate.format(dateFormatter));

        // 3. NCX - Nomor Cabang Tujuan
        stringBuilder.append(nomorCabangTujuan.toUpperCase());

        // 4. CB - Cabang Asal
        stringBuilder.append(tipeCabangAsal); // Sudah divalidasi, panjang 2 karakter

        // 5. XXXX - Jumlah Pengajuan (Nomor Urut)
        stringBuilder.append(String.format("%04d", jumlahPengajuan)); // Format 4 digit

        return stringBuilder.toString();
    }

    @Override
    public PengadaanCabangAsliTotalResponse getInputStokBarangByIdCabang(String idPengajuan) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = inputStokBarangCabangTotalDb.findById(idPengajuan).orElse(null);
            if(inputStokBarangCabangTotal == null) {
                System.out.println("Pengajuan tidak ditemukan");
                return null;
            }
            PengadaanCabangAsliTotalResponse pengadaanCabangAsliTotalResponse = new PengadaanCabangAsliTotalResponse();
            pengadaanCabangAsliTotalResponse.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
            pengadaanCabangAsliTotalResponse.setStep(inputStokBarangCabangTotal.getStep());
            pengadaanCabangAsliTotalResponse.setFlag(inputStokBarangCabangTotal.getFlagCabang());
            pengadaanCabangAsliTotalResponse.setTotalHarga(String.valueOf(inputStokBarangCabangTotal.getTotalHarga()));
            pengadaanCabangAsliTotalResponse.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
            List<InputStokBarangCabang> inputStokBarangCabangList = getInputStokBarangByIdPengajuan(idPengajuan);
            List<PengadaanCabangAsliResponse> pengadaanCabangAsliResponseList = new ArrayList<>();
            assert inputStokBarangCabangList != null;
            for(InputStokBarangCabang inputStokBarangCabang : inputStokBarangCabangList) {
                inputStokBarangCabang.setStokBarangPusatSaatIni(getStokBarangPusatSaatIni(Integer.valueOf(inputStokBarangCabang.getKodeBarang())));
                inputStokBarangCabangDb.save(inputStokBarangCabang);
                PengadaanCabangAsliResponse pengadaanCabangAsliResponse = mapToInputStokBarangResponse(inputStokBarangCabang);
                pengadaanCabangAsliResponseList.add(pengadaanCabangAsliResponse);
            }
            pengadaanCabangAsliTotalResponse.setListInputStokBarang(pengadaanCabangAsliResponseList);
            return pengadaanCabangAsliTotalResponse;
        } catch (Exception e) {
            System.out.println("Terjadi kesalahan");
            return null;
        }
    }

    private Integer getStokBarangPusatSaatIni(Integer kodeBarang) {
        try {
            List<StokBarang> stokBarangPusatList = getStokBarangByNomorCabang("001");
            for (StokBarang stokBarang : stokBarangPusatList) {
                if (stokBarang.getKodeBarang().equals(kodeBarang)) {
                    return stokBarang.getStokBarang();
                }
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
        return 0;
    }

    @Override
    public Boolean revisiInputStokBarangCabang(List<InputStokBarangRequest> listInputStokBarang, String nomorCabang, String idPengajuan) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = inputStokBarangCabangTotalDb.findById(idPengajuan).orElse(null);
            if(inputStokBarangCabangTotal == null) {
                System.out.println("Pengajuan tidak ditemukan");
                return false;
            }
            inputStokBarangCabangTotal.setStep(5);
            List<StokBarang> stokBarangList = getStokBarangByNomorCabang(nomorCabang);
            List<StokBarang> stokBarangPusatList = getStokBarangByNomorCabang("001");
            for(InputStokBarangRequest inputStokBarangRequest: listInputStokBarang) {
                StokBarang stokBarang = getStokBarangByKodeBarang(inputStokBarangRequest.getKodeBarang(), stokBarangList);
                StokBarang stokBarangPusat = getStokBarangByKodeBarang(inputStokBarangRequest.getKodeBarang(), stokBarangPusatList);
                if(stokBarangPusat != null) {
                    if(stokBarang != null) {
                        Integer stokSaatIni = stokBarang.getStokBarang();
                        Integer stokPusatSaatIni = stokBarangPusat.getStokBarang();
                        stokBarang.setStokBarang(inputStokBarangRequest.getStokInput() + stokSaatIni);
                        stokBarangPusat.setStokBarang(stokPusatSaatIni - inputStokBarangRequest.getStokInput());
                        stokBarangRepository.save(stokBarang);
                        logStokBarangService.createLogStokBarang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang(), stokSaatIni, stokBarang.getStokBarang(), idPengajuan, "PCA");
                    } else {
                        Barang barang = getBarangByKodeBarang(inputStokBarangRequest.getKodeBarang());
                        if(barang == null) {
                            System.out.println("Barang tidak ditemukan");
                            return false;
                        }
                        Integer stokPusatSaatIni = stokBarangPusat.getStokBarang();
                        StokBarang stokBarangBaru = new StokBarang();
                        stokBarangBaru.setKodeBarang(inputStokBarangRequest.getKodeBarang());
                        stokBarangBaru.setNamaBarang(barang.getNamaBarang());
                        stokBarangBaru.setNomorCabang(nomorCabang);
                        stokBarangBaru.setStokBarang(inputStokBarangRequest.getStokInput());
                        stokBarangPusat.setStokBarang(stokPusatSaatIni - inputStokBarangRequest.getStokInput());
                        stokBarangBaru.setHargaBarang(barang.getHargaBarang());
                        stokBarangRepository.save(stokBarangBaru);
                        logStokBarangService.createLogStokBarang(stokBarangBaru.getKodeBarang(), stokBarangBaru.getNomorCabang(), 0, stokBarangBaru.getStokBarang(), idPengajuan, "PCA");
                    }
                } else {
                    System.out.println("Stok Barang Tidak Ditemukan");
                    return false;
                }
            }
            NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
            notifikasiRequestDTO.setIdPengajuan(idPengajuan);
            notifikasiRequestDTO.setRolePengirim("Kepala Operasional Cabang");
            notifikasiRequestDTO.setNomorCabang(nomorCabang);
            notifikasiRequestDTO.setRolePenerima("Kepala Departemen SDM dan Umum");
            notifikasiRequestDTO.setIsiNotifikasi("Terdapat revisi pengadaan stok barang cabang asli");
            notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
            return true;
        }
        catch (Exception e) {
            System.out.println("Terjadi kesalahan" + e.getMessage());
            return false;
        }
    }

    private List<StokBarang> getStokBarangByNomorCabang(String nomorCabang) {
        List<StokBarang> stokBarangList = stokBarangRepository.findAll();
        List<StokBarang> result = new ArrayList<>();
        for (StokBarang stokBarang : stokBarangList) {
            if (stokBarang.getNomorCabang().equals(nomorCabang)) {
                result.add(stokBarang);
            }
        }
        return result;
    }

    private StokBarang getStokBarangByKodeBarang(Integer kodeBarang, List<StokBarang> stokBarangList) {
        for (StokBarang stokBarang : stokBarangList) {
            if (stokBarang.getKodeBarang().equals(kodeBarang)) {
                return stokBarang;
            }
        }
        return null;
    }

    private PengadaanCabangAsliResponse mapToInputStokBarangResponse(InputStokBarangCabang inputStokBarangCabang) {
        PengadaanCabangAsliResponse response = new PengadaanCabangAsliResponse();
        response.setKodeBarang(inputStokBarangCabang.getKodeBarang());
        response.setNamaBarang(inputStokBarangCabang.getNamaBarang());
        response.setStokPusatSaatIni(String.valueOf(inputStokBarangCabang.getStokBarangPusatSaatIni()));
        response.setStokSaatIni(String.valueOf(inputStokBarangCabang.getStokBarangCabangSaatIni()));
        response.setStokInput(String.valueOf(inputStokBarangCabang.getStokInput()));
        System.out.println("response: " + response.getStokInput());
        response.setHargaBarang(String.valueOf(inputStokBarangCabang.getHargaBarang()));

        return response;
    }

    private Barang getBarangByKodeBarang(Integer kodeBarang) {
        List<Barang> barangList = barangDb.findAll();
        for(Barang barang : barangList) {
            if(barang.getKodeBarang().equals(kodeBarang)) {
                return barang;
            }
        }
        return null;
    }

    private List<InputStokBarangCabang> getInputStokBarangByIdPengajuan(String idPengajuan) {
        try {
            List<InputStokBarangCabang> inputStokBarangCabangList = inputStokBarangCabangDb.findAll();
            List<InputStokBarangCabang> result = new ArrayList<>();
            for(InputStokBarangCabang inputStokBarangCabang: inputStokBarangCabangList) {
                if(inputStokBarangCabang.getIdPengajuan().equals(idPengajuan)) {
                    result.add(inputStokBarangCabang);
                }
            }
            return result;
        } catch (Exception e) {
            System.out.println("Pengajuan Tidak Ditemukan " + e.getMessage());
            return null;
        }

    }

    private boolean updateStokCabang(String idPengajuan) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = inputStokBarangCabangTotalDb.findById(idPengajuan).orElse(null);
            if (inputStokBarangCabangTotal == null) {
                System.out.println("Pengajuan tidak ditemukan");
                return false;
            }
            List<InputStokBarangCabang> inputStokBarangCabangList = getInputStokBarangByIdPengajuan(idPengajuan);
            List<StokBarang> stokBarangList = getStokBarangByNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
            List<StokBarang> stokBarangPusatList = getStokBarangByNomorCabang("001");

            if(stokBarangList.isEmpty()) {
                assert inputStokBarangCabangList != null;
                for(InputStokBarangCabang inputStokBarangCabang : inputStokBarangCabangList) {
                    StokBarang stokBarang = new StokBarang();
                    StokBarang stokBarangPusat = getStokBarangByKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()), stokBarangPusatList);
                    if(stokBarangPusat != null) {
                        Barang barang = barangDb.findById(Integer.valueOf(inputStokBarangCabang.getKodeBarang())).orElse(null);
                        if(barang != null) {
                            stokBarang.setKategoriBarang(barang.getKategoriBarang());
                            stokBarang.setBentuk(barang.getBentuk());
                        }
                        stokBarang.setKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()));
                        stokBarang.setNamaBarang(inputStokBarangCabang.getNamaBarang());

                        stokBarang.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                        stokBarang.setStokBarang(inputStokBarangCabang.getStokInput());
                        stokBarang.setHargaBarang(inputStokBarangCabang.getHargaBarang());

                        stokBarangRepository.save(stokBarang);
                        logStokBarangService.createLogStokBarang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang(), 0, stokBarang.getStokBarang(), inputStokBarangCabang.getIdPengajuan(), "PCA");
                    } else {
                        return false;
                    }
                }
                return true;
            } else {
                assert inputStokBarangCabangList != null;
                for(InputStokBarangCabang inputStokBarangCabang : inputStokBarangCabangList) {
                    StokBarang stokBarang = getStokBarangByKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()), stokBarangList);
                    StokBarang stokBarangPusat = getStokBarangByKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()), stokBarangPusatList);
                    System.out.println(stokBarangPusat);
                    if(stokBarangPusat != null) {
                        if(stokBarang != null) {
                            Integer stokSebelum = stokBarang.getStokBarang();
                            stokBarang.setStokBarang(stokBarang.getStokBarang() + inputStokBarangCabang.getStokInput());
                            stokBarangPusat.setStokBarang(stokBarangPusat.getStokBarang() - inputStokBarangCabang.getStokInput());
                            stokBarangRepository.save(stokBarang);
                            stokBarangRepository.save(stokBarangPusat);
                            logStokBarangService.createLogStokBarang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang(), stokSebelum, stokBarang.getStokBarang(), inputStokBarangCabang.getIdPengajuan(), "PCA");
                        } else {
                            StokBarang stokBarangBaru = new StokBarang();
                            Barang barang = barangDb.findById(Integer.valueOf(inputStokBarangCabang.getKodeBarang())).orElse(null);
                            if(barang != null) {
                                stokBarangBaru.setKategoriBarang(barang.getKategoriBarang());
                                stokBarangBaru.setBentuk(barang.getBentuk());
                            }
                            stokBarangBaru.setKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()));
                            stokBarangBaru.setNamaBarang(inputStokBarangCabang.getNamaBarang());
                            stokBarangBaru.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                            stokBarangBaru.setStokBarang(inputStokBarangCabang.getStokInput());
                            stokBarangBaru.setHargaBarang(inputStokBarangCabang.getHargaBarang());
                            stokBarangPusat.setStokBarang(stokBarangPusat.getStokBarang() - inputStokBarangCabang.getStokInput());
                            stokBarangRepository.save(stokBarangBaru);
                            logStokBarangService.createLogStokBarang(stokBarangBaru.getKodeBarang(), stokBarangBaru.getNomorCabang(), 0, stokBarangBaru.getStokBarang(), inputStokBarangCabang.getIdPengajuan(), "PCA");
                        }
                    } else {
                        System.out.println("Stok Barang Pusat tidak ditemukan");
                        return false;
                    }
                }
                return true;
            }
        } catch (Exception e) {
            System.out.println("Terjadi Error" + e.getMessage());
            return false;
        }
    }

    private int pendingHandler(String idPengajuan) {
        InputStokBarangCabangTotal inputStokBarangCabangTotal = inputStokBarangCabangTotalDb.findById(idPengajuan).orElse(null);
        if (inputStokBarangCabangTotal == null) {
            System.out.println("Pengajuan '" + idPengajuan + "' tidak ditemukan");
            return 8; // Pengajuan not found
        }

        List<InputStokBarangCabang> listInputStokBarang = getInputStokBarangByIdPengajuan(idPengajuan);
        // Assuming "001" is the identifier for Pusat/HQ
        List<StokBarang> stokBarangPusat = getStokBarangByNomorCabang("001");

        if (listInputStokBarang == null || listInputStokBarang.isEmpty()) {
            // If there are no items in the pengajuan, what should happen?
            // Assuming it means no stock check is needed, so it's clear based on current step.
            // Or, this could be an invalid state. For now, let's assume it proceeds.
            System.out.println("Tidak ada item stok barang pada pengajuan '" + idPengajuan + "'. Melanjutkan dengan step saat ini.");
            return inputStokBarangCabangTotal.getStep();
        }

        // It's good practice to handle if stokBarangPusat itself is null or empty,
        // though the logic below will implicitly handle it by not finding matches.
        if (stokBarangPusat == null) {
            stokBarangPusat = new ArrayList<>(); // Ensure it's not null for iteration
            System.out.println("Data stok barang pusat tidak ditemukan atau null. Semua permintaan akan dianggap pending.");
        }


        for (InputStokBarangCabang stokBarangCabang : listInputStokBarang) {
            boolean foundInPusat = false;
            String kodeBarangCabang = stokBarangCabang.getKodeBarang();
            Integer stokDiminta = stokBarangCabang.getStokInput();

            for (StokBarang stokBarangPusatItem : stokBarangPusat) {
                // Ensure consistent comparison for kodeBarang.
                // If stokBarangPusatItem.getKodeBarang() is Integer/Long, convert one side.
                // Assuming stokBarangPusatItem.getKodeBarang() can be safely converted to String,
                // or if stokBarangCabang.getKodeBarang() can be parsed to the type of stokBarangPusatItem.getKodeBarang().
                // Let's assume stokBarangPusatItem.getKodeBarang() is also a String or can be treated as one for comparison.
                // If not, adjust the comparison: e.g., String.valueOf(stokBarangPusatItem.getKodeBarang())
                if (Objects.equals(kodeBarangCabang, String.valueOf(stokBarangPusatItem.getKodeBarang()))) { // Adjust if types differ
                    foundInPusat = true;
                    Integer stokHQ = stokBarangPusatItem.getStokBarang();

                    if (stokHQ == null || stokHQ < stokDiminta) {
                        System.out.println("Stok HQ tidak cukup atau null untuk barang " + kodeBarangCabang + ". Diminta: " + stokDiminta + ", Tersedia HQ: " + (stokHQ == null ? "N/A" : stokHQ));
                        return 6; // pending: HQ stock insufficient or null
                    }
                    // If found and stock is sufficient, break from inner loop to check next cabang item
                    break;
                }
            }

            // After checking all pusat stock, if the item was not found in pusat at all
            if (!foundInPusat) {
                System.out.println("Barang " + kodeBarangCabang + " tidak ditemukan di stok HQ.");
                return 6;
            }
        }

        // If all items were found in pusat and stock was sufficient for all
        System.out.println("Semua item pada pengajuan '" + idPengajuan + "' memiliki stok yang cukup di HQ.");
        return inputStokBarangCabangTotal.getStep(); // all stock sufficient -> clear
    }
}
