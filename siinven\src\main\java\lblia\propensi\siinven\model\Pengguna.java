package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="pengguna")
public class Pengguna {

    public enum RolePengguna {
        STAF_GUDANG_PELAKSANA_UMUM,
        STAF_INVENTARISASI,
        STAF_PENGADAAN_DAN_PEMBELIAN,
        KEPALA_DEPARTEMEN_SDM_DAN_UMUM,
        DIREKTUR_UTAMA,
        STAF_KEUANGAN,
        ADMIN,
        KEPALA_OPERASIONAL_CABANG
    }

    @Id
    private String idKaryawan;

    @Column(name = "nama_lengkap", nullable = true)
    private String namaLengkap;

    @Column(name = "username", nullable = false, unique = true)
    private String username;

    @Column(name = "email", nullable = true)
    private String email;

    @Column(name="password", nullable = false)
    private String password;

    @NotNull
    @Pattern(regexp = "Staf Gudang Pelaksana Umum|Staf Inventarisasi|Staf Pengadaan dan Pembelian|Kepala Departemen SDM dan Umum|Direktur Utama|Staf keuangan|Admin|Kepala Operasional Cabang",
            message = "Invalid role")
//    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false)
    private String role;

    @Column(name = "nomor_telepon", nullable = true)
    private String nomorTelepon;

    @Column(name = "nomor_cabang", nullable = true)
    private String nomorCabang;

    @Column(name = "is_deleted", nullable = true)
    private Date isDeleted;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "cabang_kerjasama", referencedColumnName = "nomor_cabang")
    private CabangKerjaSama cabangKerjaSama;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "cabang", referencedColumnName = "nomor_cabang")
    private CabangAsli cabang;
}
