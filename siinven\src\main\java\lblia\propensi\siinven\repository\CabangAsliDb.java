package lblia.propensi.siinven.repository;

import lblia.propensi.siinven.model.CabangAsli;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CabangAsliDb extends JpaRepository<CabangAsli, String> {
    // Find all active branches (not deleted)
    @Query("SELECT c FROM CabangAsli c WHERE c.deletedAt IS NULL")
    List<CabangAsli> findAllActiveBranches();

    // Find branch by name
    CabangAsli findByNamaCabang(String namaCabang);
}