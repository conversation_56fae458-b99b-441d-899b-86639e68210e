package lblia.propensi.siinven.service.CabangKerjaSama;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.persistence.EntityNotFoundException;
import lblia.propensi.siinven.dto.request.CabangKerjaSama.CabangKerjaSamaRequestDTO;
import lblia.propensi.siinven.dto.response.CabangKerjaSama.CabangKerjaSamaResponseDTO;
import lblia.propensi.siinven.model.CabangKerjaSama;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.repository.PenggunaDb;
import lblia.propensi.siinven.repository.CabangKerjaSamaDb;
import lblia.propensi.siinven.service.UserRestService;

@Service
public class CabangKerjaSamaServiceImpl implements CabangKerjaSamaService {
    @Autowired
    UserRestService userRestService;

    @Autowired
    PenggunaDb penggunaDb;

    @Autowired
    CabangKerjaSamaDb cabangKerjaSamaDb;

    @Override
    public CabangKerjaSama findCabangKerjaSamaByNomorCabang(String nomorCabang) {
        return cabangKerjaSamaDb.findByNomorCabang(nomorCabang);
    }

    @Override
    public CabangKerjaSama findCabangKerjaSamaByNamaMitra(String namaMitra) {
        return cabangKerjaSamaDb.findByNamaMitra(namaMitra);
    }

    @Override
    public void createCabangKerjaSama (CabangKerjaSamaRequestDTO cabangKerjaSamaDTO) {
        // Retrieve Nomor Cabang
        String nomorCabangFromDTO = cabangKerjaSamaDTO.getNomorCabang();

        // Check if the Kepala Operasional exists
        Pengguna kepalaOperasional = userRestService.getUserByIdPengguna(cabangKerjaSamaDTO.getIdKaryawanKepalaOperasional());
        if (kepalaOperasional == null) {
            throw new EntityNotFoundException("Pengguna dengan ID " + cabangKerjaSamaDTO.getIdKaryawanKepalaOperasional() + " tidak ditemukan.");
        }

        // Check if the Kepala Operasional is already assigned to another Cabang
        CabangKerjaSama existingCabang = cabangKerjaSamaDb.findByKepalaOperasionalCabang(kepalaOperasional);
        if (existingCabang != null && !existingCabang.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Operasional sudah terdaftar di cabang lain dengan nomor %s.", existingCabang.getNomorCabang())
            );
        }

        CabangKerjaSama cabangKerjaSama = new CabangKerjaSama();
        cabangKerjaSama.setNomorCabang(nomorCabangFromDTO);
        cabangKerjaSama.setNamaMitra(cabangKerjaSamaDTO.getNamaMitra());
        cabangKerjaSama.setAlamat(cabangKerjaSamaDTO.getAlamat());
        cabangKerjaSama.setKontak(cabangKerjaSamaDTO.getKontak());
        cabangKerjaSama.setJumlahKaryawan(cabangKerjaSamaDTO.getJumlahKaryawan());
        cabangKerjaSama.setJamOperasional(cabangKerjaSamaDTO.getJamOperasional());
        cabangKerjaSama.setMasaBerlakuKontrak(cabangKerjaSamaDTO.getMasaBerlakuKontrak());
        cabangKerjaSama.setKepalaOperasionalCabang(kepalaOperasional);

        // Ensure daftarKaryawan is initialized
        if (cabangKerjaSama.getDaftarKaryawan() == null) {
            cabangKerjaSama.setDaftarKaryawan(new ArrayList<>());
        }

        // Ensure Kepala Operasional is added to daftarKaryawan
        if (!cabangKerjaSama.getDaftarKaryawan().contains(kepalaOperasional)) {
            cabangKerjaSama.getDaftarKaryawan().add(kepalaOperasional);
        }

        cabangKerjaSamaDb.save(cabangKerjaSama);

        // Set nomorCabang for Kepala Operasional Cabang
        kepalaOperasional.setNomorCabang(cabangKerjaSama.getNomorCabang());

        // Assign cabang kerja sama to Kepala Operasional
        kepalaOperasional.setCabangKerjaSama(cabangKerjaSama);

        penggunaDb.save(kepalaOperasional);

    }

    @Override
    public CabangKerjaSamaResponseDTO getCabangKerjaSamaByNomorCabang(String nomorCabang) {
        var cabangKerjaSama = findCabangKerjaSamaByNomorCabang(nomorCabang);

        if (cabangKerjaSama == null) {
            throw new EntityNotFoundException(
                String.format("Cabang kerja sama dengan nomor cabang %s tidak ditemukan", nomorCabang)
            );
        }
        
        return cabangKerjaSamaTOCabangKerjaSamaResponseDTO(cabangKerjaSama);
    }

    @Override
    public List<CabangKerjaSamaResponseDTO> getListCabangKerja() {
        List<CabangKerjaSama> listCabangKerjaSama = cabangKerjaSamaDb.findAll();
        List<CabangKerjaSamaResponseDTO> listCabangKerjaSamaDTO = new ArrayList<>();
        for (CabangKerjaSama cks : listCabangKerjaSama) {
            listCabangKerjaSamaDTO.add(cabangKerjaSamaTOCabangKerjaSamaResponseDTO(cks));
        }
        return listCabangKerjaSamaDTO;
    }

    @Override 
    public void updateCabangKerjaSama(CabangKerjaSamaRequestDTO cabangKerjaSamaDTO) throws Exception {
        // Retrieve Nomor Cabang
        String nomorCabangFromDTO = cabangKerjaSamaDTO.getNomorCabang();

        // Check if the Kepala Operasional exists
        Pengguna kepalaOperasional = userRestService.getUserByIdPengguna(cabangKerjaSamaDTO.getIdKaryawanKepalaOperasional());
        if (kepalaOperasional == null) {
            throw new EntityNotFoundException("Pengguna dengan ID " + cabangKerjaSamaDTO.getIdKaryawanKepalaOperasional() + " tidak ditemukan.");
        }

        // Check if the Kepala Operasional is already assigned to another Cabang
        CabangKerjaSama existingCabang = cabangKerjaSamaDb.findByKepalaOperasionalCabang(kepalaOperasional);
        if (existingCabang != null && !existingCabang.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Operasional sudah terdaftar di cabang lain dengan nomor %s.", existingCabang.getNomorCabang())
            );
        }
        
        CabangKerjaSama getCKS = findCabangKerjaSamaByNomorCabang(nomorCabangFromDTO);
        
        if (getCKS == null) {
            throw new EntityNotFoundException(
                String.format("Cabang kerja sama dengan nomor cabang %s tidak ditemukan", nomorCabangFromDTO)
            );
        }

        Pengguna mantanKepalaOperasional = getCKS.getKepalaOperasionalCabang();

        // Ensure Mantan Kepala Operasional is added to daftarKaryawan
        if (!getCKS.getDaftarKaryawan().contains(mantanKepalaOperasional)) {
            getCKS.getDaftarKaryawan().add(mantanKepalaOperasional);
        }

        getCKS.setNamaMitra(cabangKerjaSamaDTO.getNamaMitra());
        getCKS.setAlamat(cabangKerjaSamaDTO.getAlamat());
        getCKS.setKontak(cabangKerjaSamaDTO.getKontak());
        getCKS.setJumlahKaryawan(cabangKerjaSamaDTO.getJumlahKaryawan());
        getCKS.setJamOperasional(cabangKerjaSamaDTO.getJamOperasional());
        getCKS.setMasaBerlakuKontrak(cabangKerjaSamaDTO.getMasaBerlakuKontrak());
        getCKS.setKepalaOperasionalCabang(kepalaOperasional);

        // Ensure Kepala Operasional is added to daftarKaryawan
        if (!getCKS.getDaftarKaryawan().contains(kepalaOperasional)) {
            getCKS.getDaftarKaryawan().add(kepalaOperasional);
        }

        // Save the updated entity
        cabangKerjaSamaDb.save(getCKS);

        // Set nomorCabang Kepala Operasional Cabang
        kepalaOperasional.setNomorCabang(getCKS.getNomorCabang());

        // Assign cabang kerja sama to Kepala Operasional
        kepalaOperasional.setCabangKerjaSama(getCKS);

        penggunaDb.save(kepalaOperasional);
    }

    @Override
    public void deleteCabangKerjaSama(CabangKerjaSama cabangKerjaSama) {
        // Hapus referensi ke CabangKerjaSama pada daftar karyawan
        for (Pengguna pengguna : cabangKerjaSama.getDaftarKaryawan()) {
            pengguna.setCabangKerjaSama(null);
            pengguna.setNomorCabang("001");
            penggunaDb.save(pengguna);
        }

        // Hapus referensi kepala operasional cabang jika ada
        if (cabangKerjaSama.getKepalaOperasionalCabang() != null) {
            cabangKerjaSama.getKepalaOperasionalCabang().setNomorCabang("001");
            cabangKerjaSama.setKepalaOperasionalCabang(null);
        }

        List<Pengguna> allPengguna = penggunaDb.findAllByNomorCabang(cabangKerjaSama.getNomorCabang());
        for (Pengguna pengguna: allPengguna) {
            pengguna.setNomorCabang("001");
            penggunaDb.save(pengguna);
        }

        // Simpan perubahan sebelum menghapus cabang kerja sama
        cabangKerjaSamaDb.save(cabangKerjaSama);

        cabangKerjaSamaDb.delete(cabangKerjaSama);
    }

    private CabangKerjaSamaResponseDTO cabangKerjaSamaTOCabangKerjaSamaResponseDTO (CabangKerjaSama cabangKerjaSama) {
        var cabangKerjaSamaResponseDTO = new CabangKerjaSamaResponseDTO();
        cabangKerjaSamaResponseDTO.setNomorCabang(cabangKerjaSama.getNomorCabang());
        cabangKerjaSamaResponseDTO.setNamaMitra(cabangKerjaSama.getNamaMitra());
        cabangKerjaSamaResponseDTO.setAlamat(cabangKerjaSama.getAlamat());
        cabangKerjaSamaResponseDTO.setKontak(cabangKerjaSama.getKontak());
        cabangKerjaSamaResponseDTO.setJumlahKaryawan(cabangKerjaSama.getJumlahKaryawan());
        cabangKerjaSamaResponseDTO.setJamOperasional(cabangKerjaSama.getJamOperasional());
        cabangKerjaSamaResponseDTO.setMasaBerlakuKontrak(cabangKerjaSama.getMasaBerlakuKontrak());
        cabangKerjaSamaResponseDTO.setIdKaryawanKepalaOperasional(cabangKerjaSama.getKepalaOperasionalCabang().getIdKaryawan());
        cabangKerjaSamaResponseDTO.setUsernameKepalaOperasionalCabang(cabangKerjaSama.getKepalaOperasionalCabang().getUsername());
        cabangKerjaSamaResponseDTO.setCreatedAt(cabangKerjaSama.getCreatedAt().toString());
        cabangKerjaSamaResponseDTO.setUpdatedAt(cabangKerjaSama.getUpdatedAt().toString());
        return cabangKerjaSamaResponseDTO;
    }
}
