package lblia.propensi.siinven.dto.response.tren_permintaan_buku;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RataRataPemesananBukuResponseDTO {
    private String monthYearString; // Misal "JANUARY-2025"
    private String monthYear; // Misal "1-2025"
    private String namaBarang; // Represents the book title
    private Double averageOrders; // Represents the average number of orders
    private Integer totalOrders; // Represents the total number of orders
    private String nomorCabang; // Represents the branch number
}