package lblia.propensi.siinven.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UnderstockedItemResponseDTO {
    private String kodeBarang;
    private String namaBarang;
    private String kategoriBarang;
    private Integer stokDiminta;
    private Integer stokPusat;
    private String namaCabang;
    private String status; // "valid" or "understocked"
}

