package lblia.propensi.siinven.service;

import lblia.propensi.siinven.model.StokCabang;

import java.util.List;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.model.StokCabang;

public interface StokCabangService {
    BaseResponseDTO<List<StokCabang>> getAllStokCabang();
    BaseResponseDTO<StokCabang> addStokCabang(StokCabang stokCabang);
    StokCabang getCabangData();
    void saveStokCabangData(StokCabang stokCabang);


}