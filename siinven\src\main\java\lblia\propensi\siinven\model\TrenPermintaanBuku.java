package lblia.propensi.siinven.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.*;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="permintaan_buku_historis")
public class TrenPermintaanBuku {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @NotNull
    @Column(name = "kode_barang", nullable = false)
    private Integer kodeBarang;

    @NotNull
    @Column(name = "nama_barang", nullable = false)
    private String namaBarang;

    @NotNull
    @Column(name = "kategori_barang", nullable = false)
    private String kategoriBarang; // Harusnya selalu "Buku"

    @Column(name = "nomor_cabang", nullable = true)
    private String nomorCabang;

    @Column(name = "stok_barang_pusat", nullable = true)
    private int stokBarangSaatIni;

    @Column(name = "stok_input", nullable = true)
    private int stokInput;

    @Column(name="tanggal_pemesanan")
    private LocalDateTime tanggalPemesanan;
}
