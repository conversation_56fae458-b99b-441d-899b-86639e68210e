package lblia.propensi.siinven.dto.request.authentication;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PenggunaRegisterDTO {
    @NotBlank
    private String idKaryawan;

    @NotBlank
    private String username;

    private String namaLengkap;

    @Email
    private String email;

    @NotBlank
    private String password;

    @NotNull
    private String role;

    private String nomorTelepon;

    @NotBlank
    private String nomorCabang;
}
