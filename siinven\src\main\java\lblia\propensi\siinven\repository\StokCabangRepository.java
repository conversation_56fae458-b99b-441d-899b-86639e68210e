package lblia.propensi.siinven.repository;

import java.util.List;
import java.util.Optional;

import lblia.propensi.siinven.model.StokCabang;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface StokCabangRepository extends JpaRepository<StokCabang, Integer> {
    /**
     * Mencari data stok cabang berdasarkan kode barang
     * @param kodeBarang kode barang yang dicari
     * @return Optional berisi data stok cabang jika ditemukan
     */
    Optional<StokCabang> findByKodeBarang(Integer kodeBarang);

}

