package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.request.PengecekanStokDTO;
import lblia.propensi.siinven.dto.response.RiwayatPengecekanDTO;
import lblia.propensi.siinven.model.PengecekanStok;

import java.util.List;

public interface PengecekanStokService {
    PengecekanStokDTO updateStokAktual(String idPengajuan, String kodeBarang, Integer stokAktual, String catatan);
    List<PengecekanStokDTO> getAllBarangForPengecekan(String nomorCabang);
    BaseResponseDTO<String> submitPengecekanStok(List<PengecekanStokDTO> listHasilPengecekan, String idPetugas, String nomorCabang);
    List<RiwayatPengecekanDTO> getAllRiwayatPengecekan();
    List<RiwayatPengecekanDTO> getRiwayatPengecekanByCabang(String nomorCabang);
    List<PengecekanStokDTO> getDetailPengecekanById(String idPengajuan);
    PengecekanStokDTO getDetailPengecekanByKodeBarang(String kodeBarang, String idPengajuan);
    BaseResponseDTO<String> updateStatusPengecekan(String idPengajuan, String statusPengecekan);
}