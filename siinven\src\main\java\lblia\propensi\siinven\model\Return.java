package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "return_stok_barang")
public class Return {
    
    @Id
    private String idInputStokBarangReturn;

    @Column(name = "id_pengajuan", nullable = true)
    private String idPengajuan; // Siapa yang mengajukan retur (bisa user ID)

    @Column(name = "kode_barang", nullable = true)
    private Integer kodeBarang;

    @Column(name = "nama_barang", nullable = true)
    private String namaBarang;

    @Column(name = "stok_barang_pusat", nullable = true)
    private int stokBarangSaatIni;

    @Column(name = "stok_input", nullable = true)
    private int stokInput; // Jumlah barang yang ingin diretur

    @Pattern(regexp = "Dibuang|Dikembalikan|Dijual|Disumbangkan", message = "Perlakuan harus salah satu dari: Dibuang, Dikembalikan, Dijual, Disumbangkan")
    @Column(name = "perlakuan", nullable = true)
    private String perlakuan; // Apa yang dilakukan dengan barangnya

    @Column(name = "harga_barang", nullable = true)
    private Double hargaBarang;

    @Column(name = "alasan_return", nullable = true)
    private String alasanReturn; // Alasan retur

    // Tambahan baru:
    @Column(name = "status_approval", nullable = false)
    private String statusApproval = "MENUNGGU"; 
    // Status retur: MENUNGGU, DISETUJUI, DITOLAK

    @Column(name = "jumlah_dikonfirmasi", nullable = true)
    private Integer jumlahDikonfirmasi;
    // Berapa jumlah yang dikonfirmasi diterima oleh gudang

    @Column(name = "status_retur", nullable = false)
    private String statusRetur = "PENGAJUAN";
    // Status proses retur: PENGAJUAN, DIKIRIM, DITERIMA, SELESAI

    @PrePersist
    protected void onCreate() {
        if (idInputStokBarangReturn == null) {
            // Format: PGN + tanggal dalam format yyyyMMddHHmmss
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMddHHmmss");
            String dateStr = sdf.format(new java.util.Date());
            idInputStokBarangReturn = "RETR" + dateStr;
        }
    }
}
