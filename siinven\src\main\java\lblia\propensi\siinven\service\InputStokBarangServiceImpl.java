package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.*;
import lblia.propensi.siinven.dto.request.pusat.InputStokBarangListRequest;
import lblia.propensi.siinven.dto.request.pusat.InputStokBarangRequest;
import lblia.propensi.siinven.dto.request.pusat.PersetujuanInputBarangPusatRequest;
import lblia.propensi.siinven.dto.response.CopyBarangResponse;
import lblia.propensi.siinven.dto.response.pusat.InputStokBarangResponse;
import lblia.propensi.siinven.dto.response.pusat.InputStokBarangTotalResponse;
import lblia.propensi.siinven.model.*;
import lblia.propensi.siinven.repository.*;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class InputStokBarangServiceImpl implements InputStokBarangService {

    private final BarangDb barangDb;
    private final StokBarangRepository stokBarangRepository;
    private final InputStokBarangPusatDb inputStokBarangPusatDb;
    private final InputStokBarangPusatTotalDb inputStokBarangPusatTotalDb;
    private final NotifikasiRestService notifikasiRestService;
    private final LogStokBarangService logStokBarangService;
    private final KeteranganPengajuanDb keteranganPengajuanDb;

    public InputStokBarangServiceImpl(BarangDb barangDb, StokBarangRepository stokBarangRepository,
                                      InputStokBarangPusatDb inputStokBarangPusatDb,
                                      InputStokBarangPusatTotalDb inputStokBarangPusatTotalDb,
                                      NotifikasiRestService notifikasiRestService, LogStokBarangService logStokBarangService,
                                      KeteranganPengajuanDb keteranganPengajuanDb) {
        this.barangDb = barangDb;
        this.stokBarangRepository = stokBarangRepository;
        this.inputStokBarangPusatDb = inputStokBarangPusatDb;
        this.inputStokBarangPusatTotalDb = inputStokBarangPusatTotalDb;
        this.notifikasiRestService = notifikasiRestService;
        this.logStokBarangService = logStokBarangService;
        this.keteranganPengajuanDb = keteranganPengajuanDb;
    }


    @Override
    public List<CopyBarangResponse> getAllBarang() {
        List<Barang> barangList = barangDb.findAll();
        List<CopyBarangResponse> copyBarangResponseList = new ArrayList<>();
        List<StokBarang> stokBarangList = GetStokBarangByNomorCabang("001");
        System.out.println(stokBarangList);
        for(Barang barang : barangList) {
            CopyBarangResponse copyBarangResponse = new CopyBarangResponse();
            copyBarangResponse.setKodeBarang(barang.getKodeBarang());
            copyBarangResponse.setNamaBarang(barang.getNamaBarang());
            copyBarangResponse.setHargaBarang(barang.getHargaBarang());
            System.out.println("Barang: " + barang.getKodeBarang());
            for (StokBarang stokBarang : stokBarangList) {
                System.out.println("Stok Barang: " + stokBarang.getKodeBarang() + "Barang: " + barang.getKodeBarang());
                if (Objects.equals(stokBarang.getKodeBarang(), barang.getKodeBarang())) {
                    System.out.println(stokBarang.getKodeBarang());
                    copyBarangResponse.setStokBarang(stokBarang.getStokBarang());
                }
            }
            if(copyBarangResponse.getStokBarang() == null) {
                copyBarangResponse.setStokBarang(0);
            }

            copyBarangResponseList.add(copyBarangResponse);
        }

        return copyBarangResponseList;
    }

    @Override
    public List<HashMap<String, String>> getAllPengajuan() {
        List<InputStokBarangPusatTotal> inputStokBarangPusatList = inputStokBarangPusatTotalDb.findAll();
        List<HashMap<String, String>> pengajuanList = new ArrayList<>();
        for(InputStokBarangPusatTotal inputStokBarangPusat : inputStokBarangPusatList) {
            HashMap<String, String> pengajuan = new HashMap<>();
            pengajuan.put("idPengajuan", String.valueOf(inputStokBarangPusat.getIdPengajuan()));
            pengajuan.put("step", String.valueOf(inputStokBarangPusat.getStep()));

            pengajuan.put("waktuPengajuan", inputStokBarangPusat.getWaktuPengajuan().toString());
            pengajuanList.add(pengajuan);
        }
        return pengajuanList;
    }

    @Override
    public Boolean persetujuanInputBarangPusat(PersetujuanInputBarangPusatRequest request, String role) {
        try {
            InputStokBarangPusatTotal inputStokBarangPusatTotal = inputStokBarangPusatTotalDb.findById(request.getIdPengajuan()).orElse(null);
            if (inputStokBarangPusatTotal == null) {
                System.out.println("Pengajuan dengan id: " + request.getIdPengajuan() + " tidak ditemukan.");
                return false;
            }
            if (inputStokBarangPusatTotal.getStep() == 1 && role.equals("Kepala Departemen SDM dan Umum")) {
                if(request.getStatus()) {
                    inputStokBarangPusatTotal.setStep(2);
                    inputStokBarangPusatTotal.setTotalHarga(inputStokBarangPusatTotal.getTotalHarga());
                    inputStokBarangPusatTotalDb.save(inputStokBarangPusatTotal);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Kepala Departemen SDM dan Umum");
                    notifikasiRequestDTO.setRolePenerima("Staf keuangan");
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang untuk pusat telah disetujui");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
                    return true;
                } else {
                    inputStokBarangPusatTotal.setStep(0);
                    inputStokBarangPusatTotalDb.save(inputStokBarangPusatTotal);

                    KeteranganPengajuan keteranganPengajuan = new KeteranganPengajuan();

                    keteranganPengajuan.setIdPengajuan(request.getIdPengajuan());
                    keteranganPengajuan.setKeterangan(request.getKeterangan());
                    keteranganPengajuanDb.save(keteranganPengajuan);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Kepala Departemen SDM dan Umum");
                    notifikasiRequestDTO.setRolePenerima("Staf Gudang Pelaksana Umum");
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang untuk pusat minta direvisi");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
                    return false;
                }
            }
            else if (inputStokBarangPusatTotal.getStep() == 2 && role.equals("Staf keuangan")) {
                if(request.getStatus()) {
                    inputStokBarangPusatTotal.setStep(3);
                    inputStokBarangPusatTotal.setTotalHarga(inputStokBarangPusatTotal.getTotalHarga());
                    inputStokBarangPusatTotalDb.save(inputStokBarangPusatTotal);
                    updateStokBarang(inputStokBarangPusatTotal.getIdPengajuan());

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Staf keuangan");
                    notifikasiRequestDTO.setRolePenerima("Staf Gudang Pelaksana Umum");
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang untuk pusat telah disetujui");
                    return true;
                } else {
                    inputStokBarangPusatTotal.setStep(0);
                    inputStokBarangPusatTotalDb.save(inputStokBarangPusatTotal);

                    KeteranganPengajuan keteranganPengajuan = new KeteranganPengajuan();

                    keteranganPengajuan.setIdPengajuan(request.getIdPengajuan());
                    keteranganPengajuan.setKeterangan(request.getKeterangan());
                    keteranganPengajuanDb.save(keteranganPengajuan);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Staf keuangan");
                    notifikasiRequestDTO.setRolePenerima("Staf Gudang Pelaksana Umum");
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang untuk pusat minta direvisi");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);

                    return false;
                }
            } else {
                System.out.println("Role tidak sesuai.");
                return false;
            }
        } catch (Exception exception) {
            System.out.println("Error: " + exception.getMessage());
            return false;
        }
    }

    private void updateStokBarang(String idPengajuan) {
        try {
            InputStokBarangPusatTotal inputStokBarangPusatTotal = inputStokBarangPusatTotalDb.findById(idPengajuan).orElse(null);

            if(inputStokBarangPusatTotal == null) {
                System.out.println("Pengajuan dengan id: " + idPengajuan + " tidak ditemukan.");
                return;
            }

            List<InputStokBarangPusat> inputStokBarangPusatList = getInputStokBarangPusatById(inputStokBarangPusatTotal.getIdPengajuan());

            List<StokBarang> stokBarangList = GetStokBarangByNomorCabang("001");
            if(stokBarangList.isEmpty()) {
                for(InputStokBarangPusat inputStokBarangPusat : inputStokBarangPusatList) {
                    StokBarang stokBarang = new StokBarang();
                    stokBarang.setKodeBarang(Integer.valueOf(inputStokBarangPusat.getKodeBarang()));
                    stokBarang.setNamaBarang(inputStokBarangPusat.getNamaBarang());
                    stokBarang.setStokBarang(inputStokBarangPusat.getStokInput());
                    stokBarang.setHargaBarang(inputStokBarangPusat.getHargaBarang());
                    stokBarang.setNomorCabang("001");
                    stokBarangRepository.save(stokBarang);
                    logStokBarangService.createLogStokBarang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang(), 0, stokBarang.getStokBarang(), idPengajuan, "PP");
                }
            } else {
                for(InputStokBarangPusat inputStokBarangPusat : inputStokBarangPusatList) {
                    StokBarang stokBarang = getStokBarangByKode(Integer.valueOf(inputStokBarangPusat.getKodeBarang()), stokBarangList);
                    if(stokBarang != null) {
                        Integer stokAwal = stokBarang.getStokBarang();
                        stokBarang.setStokBarang(stokBarang.getStokBarang() + inputStokBarangPusat.getStokInput());
                        stokBarangRepository.save(stokBarang);
                        logStokBarangService.createLogStokBarang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang(), stokAwal, stokBarang.getStokBarang(), inputStokBarangPusatTotal.getIdPengajuan(), "PP");
                    } else {
                        StokBarang newStokBarang = new StokBarang();
                        newStokBarang.setKodeBarang(Integer.valueOf(inputStokBarangPusat.getKodeBarang()));
                        newStokBarang.setNamaBarang(inputStokBarangPusat.getNamaBarang());
                        newStokBarang.setStokBarang(inputStokBarangPusat.getStokInput());
                        newStokBarang.setHargaBarang(inputStokBarangPusat.getHargaBarang());
                        newStokBarang.setNomorCabang("001");
                        stokBarangRepository.save(newStokBarang);
                        logStokBarangService.createLogStokBarang(newStokBarang.getKodeBarang(), newStokBarang.getNomorCabang(), 0, newStokBarang.getStokBarang(), inputStokBarangPusatTotal.getIdPengajuan(), "PP");
                    }
                }
            }
        }
        catch (Exception exception) {
            System.out.println("Error: " + exception.getMessage());
        }
    }

    private List<InputStokBarangPusat> getInputStokBarangPusatByIdPengajuan(String idPengajuan) {
        List<InputStokBarangPusat> inputStokBarangPusatList = inputStokBarangPusatDb.findAll();
        List<InputStokBarangPusat> result = new ArrayList<>();
        for(InputStokBarangPusat inputStokBarangPusat : inputStokBarangPusatList) {
            if(inputStokBarangPusat.getIdPengajuan().equals(idPengajuan)) {
                result.add(inputStokBarangPusat);
            }
        }
        return result;
    }

    private String generateIdPengajuan() {

        String nomorCabangTujuan = "001";

        Integer jumlahPengajuan = inputStokBarangPusatDb.findAll().size() + 1;
        String tipeCabangAsal = "AS";


        if (jumlahPengajuan < 0 || jumlahPengajuan > 9999) {
            throw new IllegalArgumentException("Jumlah Pengajuan harus antara 0 dan 9999.");
        }

        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("PP");

        ZoneId zoneId = ZoneId.of("Asia/Jakarta");
        Date tanggalPengajuan = new Date();

        // Ubah Date ke LocalDate dengan zona Jakarta
        LocalDate localDate = tanggalPengajuan.toInstant()
                .atZone(zoneId)
                .toLocalDate();

        // Format menjadi YYYYMMDD
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        stringBuilder.append(localDate.format(dateFormatter));

        // 3. NCX - Nomor Cabang Tujuan
        stringBuilder.append(nomorCabangTujuan.toUpperCase());

        // 4. CB - Cabang Asal
        stringBuilder.append(tipeCabangAsal); // Sudah divalidasi, panjang 2 karakter

        // 5. XXXX - Jumlah Pengajuan (Nomor Urut)
        stringBuilder.append(String.format("%04d", jumlahPengajuan)); // Format 4 digit

        return stringBuilder.toString();
    }

    @Override
    public InputStokBarangTotalResponse inputStokBarang(InputStokBarangListRequest listInputStokBarang) {
        try {
            InputStokBarangPusatTotal inputStokBarangPusatTotal = new InputStokBarangPusatTotal();
            List<InputStokBarangResponse> inputStokBarangPusatList = new ArrayList<>();
            inputStokBarangPusatTotal.setIdPengajuan(generateIdPengajuan());
            inputStokBarangPusatTotal.setStep(1);
            inputStokBarangPusatTotal.setWaktuPengajuan(LocalDateTime.now());
            double totalHarga = 0L;

            inputStokBarangPusatTotalDb.save(inputStokBarangPusatTotal);

            for(InputStokBarangRequest listInputStokBarangRequest: listInputStokBarang.getListInputBarang()) {
                Integer kodeBarang = listInputStokBarangRequest.getKodeBarang();
                Integer jumlahInput = listInputStokBarangRequest.getStokInput();
                List<StokBarang> stokBarangList = GetStokBarangByNomorCabang("001");

                System.out.println("Kode Barang: " + kodeBarang + "Jumlah Input: " + jumlahInput);

                StokBarang stokBarang = getStokBarangByKode(kodeBarang, stokBarangList);

                if (stokBarang == null) {
                    Barang barang = getBarangByKode(kodeBarang);

                    System.out.println("Barang: " + barang.getKodeBarang());
                    if(barang == null) {
                        System.out.println("Barang dengan kode: " + kodeBarang + " tidak ditemukan.");
                        return null;
                    }
                    InputStokBarangPusat inputStokBarangPusat = new InputStokBarangPusat();
                    inputStokBarangPusat.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
                    inputStokBarangPusat.setKodeBarang(String.valueOf(kodeBarang));
                    inputStokBarangPusat.setNamaBarang(barang.getNamaBarang());
                    inputStokBarangPusat.setStokBarangSaatIni(0);
                    inputStokBarangPusat.setStokInput(jumlahInput);
                    System.out.println("Stok Barang Saat Ini: " + inputStokBarangPusat.getStokBarangSaatIni());
                    inputStokBarangPusat.setHargaBarang(barang.getHargaBarang());
                    inputStokBarangPusatDb.save(inputStokBarangPusat);
                    System.out.println("Input Stok Barang Pusat: " + inputStokBarangPusat.getStokInput() + "Stok Barang Saat Ini: " + inputStokBarangPusat.getStokBarangSaatIni());
                    inputStokBarangPusatList.add(mapToInputStokBarangResponse(inputStokBarangPusat));
                    totalHarga += barang.getHargaBarang() * jumlahInput;
                } else {
                    System.out.println("Stok Barang: " + stokBarang.getKodeBarang() + "Barang: " + kodeBarang);
                    InputStokBarangPusat inputStokBarangPusat = new InputStokBarangPusat();
                    inputStokBarangPusat.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
                    inputStokBarangPusat.setKodeBarang(String.valueOf(kodeBarang));
                    inputStokBarangPusat.setNamaBarang(stokBarang.getNamaBarang());
                    System.out.println("Stok Barang: " + stokBarang.getStokBarang());
                    inputStokBarangPusat.setStokBarangSaatIni(stokBarang.getStokBarang());
                    inputStokBarangPusat.setStokInput(jumlahInput);
                    System.out.println("Stok Barang Saat Ini: " + inputStokBarangPusat.getStokBarangSaatIni());
                    System.out.println("Jumlah Input: " + jumlahInput);
                    inputStokBarangPusat.setHargaBarang(stokBarang.getHargaBarang());
                    try {
                        inputStokBarangPusatDb.save(inputStokBarangPusat);
                    }
                    catch (Exception e) {
                        System.out.println("Error: " + e.getMessage());
                    }
                    System.out.println("Input Stok Barang Pusat: " + inputStokBarangPusat.getStokInput() + "Stok Barang Saat Ini: " + inputStokBarangPusat.getStokBarangSaatIni());
                    inputStokBarangPusatList.add(mapToInputStokBarangResponse(inputStokBarangPusat));
                    totalHarga += stokBarang.getHargaBarang() * jumlahInput;
                }
            }
            inputStokBarangPusatTotal.setTotalHarga(totalHarga);
            inputStokBarangPusatTotalDb.save(inputStokBarangPusatTotal);

            NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
            notifikasiRequestDTO.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
            notifikasiRequestDTO.setRolePengirim("Staf Gudang Pelaksana Umum");
            notifikasiRequestDTO.setRolePenerima("Kepala Departemen SDM dan Umum");
            notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang untuk pusat telah diajukan");
            notifikasiRestService.createNotifikasi(notifikasiRequestDTO);

            InputStokBarangTotalResponse inputStokBarangTotalResponse = new InputStokBarangTotalResponse();

            inputStokBarangTotalResponse.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
            inputStokBarangTotalResponse.setListInputStokBarang(inputStokBarangPusatList);
            inputStokBarangTotalResponse.setTotalHarga(String.valueOf(totalHarga));
            inputStokBarangTotalResponse.setStep(inputStokBarangPusatTotal.getStep());
            return inputStokBarangTotalResponse;

        } catch (Exception exception) {
            System.out.println("Error: " + exception.getMessage());
            return null;
        }

    }

    @Override
    public InputStokBarangTotalResponse getInputStokBarangById(String idPengajuan) {
        try {
            InputStokBarangPusatTotal inputStokBarangPusatTotal = inputStokBarangPusatTotalDb.findById(idPengajuan).orElse(null);
            if (inputStokBarangPusatTotal == null) {
                System.out.println("Pengajuan dengan id: " + idPengajuan + " tidak ditemukan.");
                return null;
            }

            InputStokBarangTotalResponse inputStokBarangTotalResponse = new InputStokBarangTotalResponse();
            inputStokBarangTotalResponse.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
            inputStokBarangTotalResponse.setStep(inputStokBarangPusatTotal.getStep());
            inputStokBarangTotalResponse.setTotalHarga(String.valueOf(inputStokBarangPusatTotal.getTotalHarga()));
            List<InputStokBarangPusat> inputStokBarangPusatList = getInputStokBarangPusatById(inputStokBarangPusatTotal.getIdPengajuan());
            List<InputStokBarangResponse> inputStokBarangResponseList = new ArrayList<>();
            for(InputStokBarangPusat inputStokBarangPusat : inputStokBarangPusatList) {
                InputStokBarangResponse inputStokBarangResponse = mapToInputStokBarangResponse(inputStokBarangPusat);
                inputStokBarangResponseList.add(inputStokBarangResponse);
            }
            inputStokBarangTotalResponse.setListInputStokBarang(inputStokBarangResponseList);
            return inputStokBarangTotalResponse;
        } catch (Exception exception) {
            System.out.println("Error: " + exception.getMessage());
            return null;
        }

    }

    @Override
    public InputStokBarangTotalResponse revisiInputStokBarang(RevisiPengadaanPusatDTO listInputStokBarang, String idPengajuan) {
        try {
            InputStokBarangPusatTotal inputStokBarangPusatTotal = new InputStokBarangPusatTotal();
            List<InputStokBarangResponse> inputStokBarangPusatList = new ArrayList<>();
            inputStokBarangPusatTotal.setStep(1);
            inputStokBarangPusatTotal.setWaktuPengajuan(LocalDateTime.now());
            double totalHarga = 0L;


            for(InputStokBarangRequest listInputStokBarangRequest: listInputStokBarang.getListInputBarang()) {
                Integer kodeBarang = listInputStokBarangRequest.getKodeBarang();
                Integer jumlahInput = listInputStokBarangRequest.getStokInput();
                List<StokBarang> stokBarangList = GetStokBarangByNomorCabang("001");

                System.out.println(stokBarangList);

                StokBarang stokBarang = getStokBarangByKode(kodeBarang, stokBarangList);

                if (stokBarang == null) {

                    Barang barang = getBarangByKode(kodeBarang);

                    System.out.println("Barang: " + barang.getKodeBarang());
                    if(barang == null) {
                        System.out.println("Barang dengan kode: " + kodeBarang + " tidak ditemukan.");
                        return null;
                    }
                    InputStokBarangPusat inputStokBarangPusat = new InputStokBarangPusat();
                    inputStokBarangPusat.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
                    inputStokBarangPusat.setKodeBarang(String.valueOf(kodeBarang));
                    inputStokBarangPusat.setNamaBarang(barang.getNamaBarang());
                    inputStokBarangPusat.setStokBarangSaatIni(0);
                    inputStokBarangPusat.setStokInput(jumlahInput);
                    inputStokBarangPusat.setHargaBarang(barang.getHargaBarang());
                    inputStokBarangPusatDb.save(inputStokBarangPusat);
                    inputStokBarangPusatList.add(mapToInputStokBarangResponse(inputStokBarangPusat));
                    totalHarga += barang.getHargaBarang() * jumlahInput;
                } else {
                    InputStokBarangPusat inputStokBarangPusat = new InputStokBarangPusat();
                    inputStokBarangPusat.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
                    inputStokBarangPusat.setKodeBarang(String.valueOf(kodeBarang));
                    inputStokBarangPusat.setNamaBarang(stokBarang.getNamaBarang());
                    inputStokBarangPusat.setStokBarangSaatIni(stokBarang.getStokBarang());
                    inputStokBarangPusat.setStokInput(jumlahInput);
                    inputStokBarangPusat.setHargaBarang(stokBarang.getHargaBarang());
                    inputStokBarangPusatDb.save(inputStokBarangPusat);

                    inputStokBarangPusatList.add(mapToInputStokBarangResponse(inputStokBarangPusat));
                    totalHarga += stokBarang.getHargaBarang() * jumlahInput;
                }
            }

            inputStokBarangPusatTotalDb.save(inputStokBarangPusatTotal);

            NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
            notifikasiRequestDTO.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
            notifikasiRequestDTO.setRolePengirim("Staf Gudang Pelaksana Umum");
            notifikasiRequestDTO.setRolePenerima("Kepala Departemen SDM dan Umum");
            notifikasiRequestDTO.setIsiNotifikasi("Staf Gudang telah melakukan revisi terhadap pengadaan stok barang");
            notifikasiRestService.createNotifikasi(notifikasiRequestDTO);

            NotifikasiRequestDTO notifikasiRequestDTOS = new NotifikasiRequestDTO();
            notifikasiRequestDTOS.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
            notifikasiRequestDTOS.setRolePengirim("Staf Gudang Pelaksana Umum");
            notifikasiRequestDTOS.setRolePenerima("Staf keuangan");
            notifikasiRequestDTOS.setIsiNotifikasi("Staf Gudang telah melakukan revisi terhadap pengadaan stok barang");
            notifikasiRestService.createNotifikasi(notifikasiRequestDTOS);

            InputStokBarangTotalResponse inputStokBarangTotalResponse = new InputStokBarangTotalResponse();
            inputStokBarangTotalResponse.setIdPengajuan(inputStokBarangPusatTotal.getIdPengajuan());
            inputStokBarangTotalResponse.setListInputStokBarang(inputStokBarangPusatList);
            inputStokBarangTotalResponse.setTotalHarga(String.valueOf(totalHarga));
            inputStokBarangTotalResponse.setStep(inputStokBarangPusatTotal.getStep());
            return inputStokBarangTotalResponse;
        } catch (Exception exception) {
            System.out.println("Error: " + exception.getMessage());
            return null;
        }
    }

    private List<InputStokBarangPusat> getInputStokBarangPusatById(String idPengajuan) {
        List<InputStokBarangPusat> inputStokBarangPusatList = inputStokBarangPusatDb.findAll();
        List<InputStokBarangPusat> result = new ArrayList<>();
        for(InputStokBarangPusat inputStokBarangPusat : inputStokBarangPusatList) {
            if(inputStokBarangPusat.getIdPengajuan().equals(idPengajuan)) {
                result.add(inputStokBarangPusat);
            }
        }
        return result;
    }

    private InputStokBarangResponse mapToInputStokBarangResponse(InputStokBarangPusat inputStokBarangPusat) {
        InputStokBarangResponse response = new InputStokBarangResponse();
        response.setKodeBarang(inputStokBarangPusat.getKodeBarang());
        response.setNamaBarang(inputStokBarangPusat.getNamaBarang());
        System.out.println("Stok Barang: " + inputStokBarangPusat.getStokBarangSaatIni() + "Input: " + inputStokBarangPusat.getStokInput());
        response.setStokSaatIni(String.valueOf(inputStokBarangPusat.getStokBarangSaatIni()));
        response.setStokInput(String.valueOf(inputStokBarangPusat.getStokInput()));
        System.out.println("response: " + response.getStokInput());
        response.setHargaBarang(String.valueOf(inputStokBarangPusat.getHargaBarang()));

        return response;
    }



    private List<StokBarang> GetStokBarangByNomorCabang(String nomorCabang) {
        List<StokBarang> stokBarangList = stokBarangRepository.findAll();
        List<StokBarang> result = new ArrayList<>();
        for(StokBarang stokBarang : stokBarangList) {
            if(stokBarang.getNomorCabang().equals(nomorCabang)) {
                result.add(stokBarang);
            }
        }
        return result;
    }

    private StokBarang getStokBarangByKode(Integer kodeBarang, List<StokBarang> stokBarangList) {
        for(StokBarang stokBarang : stokBarangList) {
            if(Objects.equals(stokBarang.getKodeBarang(), kodeBarang)) {
                return stokBarang;
            }
        }
        return null;
    }

    private Barang getBarangByKode(Integer kodeBarang) {
        List<Barang> barangList = barangDb.findAll();
        for(Barang barang : barangList) {
            if(barang.getKodeBarang().equals(kodeBarang)) {
                return barang;
            }
        }
        return null;
    }

}
