package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import lombok.*;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="keterangan_pengajuan")
public class KeteranganPengajuan {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer idKeteranganPengajuan;

    @Column(name = "keterangan", nullable = true)
    private String keterangan;

    @Column(name = "id_pengajuan", nullable = true)
    private String idPengajuan;

    @Column(name = "waktu_keterangan", nullable = true)
    private Date waktuKeterangan;

    @Column(name = "role_pengirim", nullable = false)
    private String rolePengirim;

    @PrePersist
    protected void onCreate() {
        if (waktuKeterangan == null) {
            waktuKeterangan = new Date();
        }
    }
}
