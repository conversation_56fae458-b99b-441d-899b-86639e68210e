package lblia.propensi.siinven.service;

import jakarta.transaction.Transactional;
import lblia.propensi.siinven.dto.request.NotifikasiRequestDTO;
import lblia.propensi.siinven.dto.request.cabang_asli.InputStokBarangCARequest;
import lblia.propensi.siinven.dto.request.cabang_asli.PersetujuanStokBarangCabangAsliRequest;
import lblia.propensi.siinven.dto.request.pusat.InputStokBarangRequest;
import lblia.propensi.siinven.dto.response.StokBarangResponseCabangDTO;
import lblia.propensi.siinven.dto.response.cabang_asli.PengadaanCabangAsliResponse;
import lblia.propensi.siinven.dto.response.cabang_asli.PengadaanCabangAsliTotalResponse;
import lblia.propensi.siinven.model.*;
import lblia.propensi.siinven.repository.*;
import lblia.propensi.siinven.model.Barang;
import lblia.propensi.siinven.model.InputStokBarangCabang;
import lblia.propensi.siinven.model.InputStokBarangCabangTotal;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.repository.BarangDb;
import lblia.propensi.siinven.repository.InputStokBarangCabangDb;
import lblia.propensi.siinven.repository.InputStokBarangCabangTotalDb;
import lblia.propensi.siinven.repository.StokBarangRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Transactional
public class PengadaanStokBarangCKSServiceImpl implements PengadaanStokBarangCKSService{
    private final BarangDb barangDb;
    private final StokBarangRepository stokBarangRepository;
    private final InputStokBarangCabangDb inputStokBarangCabangDb;
    private final InputStokBarangCabangTotalDb inputStokBarangCabangTotalDb;
    private final NotifikasiRestService notifikasiRestService;
    private final KeteranganPengajuanDb keteranganPengajuanDb;

    private final LogStokBarangService logStokBarangService;

    @Autowired
    private TrenPermintaanBukuService trenPermintaanBukuService;

    public PengadaanStokBarangCKSServiceImpl(BarangDb barangDb, StokBarangRepository stokBarangRepository,
                                             InputStokBarangCabangDb inputStokBarangCabangDb,
                                             InputStokBarangCabangTotalDb inputStokBarangCabangTotalDb,
                                             NotifikasiRestService notifikasiRestService,
                                             KeteranganPengajuanDb keteranganPengajuanDb,
                                             LogStokBarangService logStokBarangService) {
        this.barangDb = barangDb;
        this.stokBarangRepository = stokBarangRepository;
        this.inputStokBarangCabangDb = inputStokBarangCabangDb;
        this.inputStokBarangCabangTotalDb = inputStokBarangCabangTotalDb;
        this.notifikasiRestService = notifikasiRestService;
        this.keteranganPengajuanDb = keteranganPengajuanDb;
        this.logStokBarangService = logStokBarangService;
    }

    @Override
    public List<StokBarangResponseCabangDTO> getAllBarang(String nomorCabang) {
        List<Barang> barangList = barangDb.findAll();
        List<StokBarangResponseCabangDTO> copyBarangResponseList = new ArrayList<>();
        List<StokBarang> stokBarangList = getStokBarangByNomorCabang(nomorCabang);
        List<StokBarang> stokBarangPusat = getStokBarangByNomorCabang("001");
        System.out.println(stokBarangList);
        for (Barang barang : barangList) {
            StokBarangResponseCabangDTO copyBarangResponse = new StokBarangResponseCabangDTO();
            copyBarangResponse.setKodeBarang(barang.getKodeBarang());
            copyBarangResponse.setNamaBarang(barang.getNamaBarang());
            double hargaBarangCKS = barang.getHargaBarang() * 0.25;
            copyBarangResponse.setHargaBarang(barang.getHargaBarang() + hargaBarangCKS);
            System.out.println("Barang: " + barang.getKodeBarang());
            for (StokBarang stokBarang : stokBarangList) {
                System.out.println("Stok Barang: " + stokBarang.getKodeBarang() + "Barang: " + barang.getKodeBarang());
                if (Objects.equals(stokBarang.getKodeBarang(), barang.getKodeBarang())) {
                    System.out.println(stokBarang.getKodeBarang());
                    copyBarangResponse.setStokBarang(stokBarang.getStokBarang());
                }
            }
            if (copyBarangResponse.getStokBarang() == null) {
                copyBarangResponse.setStokBarang(0);
            }
            for (StokBarang stokBarang : stokBarangPusat) {
                if (Objects.equals(stokBarang.getKodeBarang(), barang.getKodeBarang())) {
                    copyBarangResponse.setStokBarangPusat(stokBarang.getStokBarang());
                }
            }

            if(copyBarangResponse.getStokBarangPusat() == null) {
                copyBarangResponse.setStokBarangPusat(0);
            }

            copyBarangResponseList.add(copyBarangResponse);
        }

        return copyBarangResponseList;

    }

    @Override
    public List<HashMap<String, String>> getAllPengajuan() {
        List<InputStokBarangCabangTotal> inputStokBarangPusatList = inputStokBarangCabangTotalDb.findAll();
        List<HashMap<String, String>> pengajuanList = new ArrayList<>();
        for (InputStokBarangCabangTotal inputStokBarangPusat : inputStokBarangPusatList) {
            HashMap<String, String> pengajuan = new HashMap<>();
            pengajuan.put("idPengajuan", String.valueOf(inputStokBarangPusat.getIdPengajuan()));
            pengajuan.put("step", String.valueOf(inputStokBarangPusat.getStep()));

            pengajuan.put("waktuPengajuan", inputStokBarangPusat.getWaktuPengajuan().toString());
            pengajuanList.add(pengajuan);
        }
        return pengajuanList;
    }

    public void buatKeterangan(String idPengajuan, String keterangan, String role) {
        KeteranganPengajuan keteranganPengajuan = new KeteranganPengajuan();
        keteranganPengajuan.setIdPengajuan(idPengajuan);
        keteranganPengajuan.setKeterangan(keterangan);
        keteranganPengajuan.setRolePengirim(role);
        keteranganPengajuan.setWaktuKeterangan(new Date());
        try {
            keteranganPengajuanDb.save(keteranganPengajuan);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Error: " + e.getMessage());
        }
    }

    @Override
    public Boolean persetujuanInputBarangCabangAsli(PersetujuanStokBarangCabangAsliRequest request, String role) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = inputStokBarangCabangTotalDb.findById(request.getIdPengajuan()).orElse(null);
            if(request.getKeterangan() != null) {
                if(!request.getKeterangan().isEmpty()) {
                    buatKeterangan(request.getIdPengajuan(), request.getKeterangan(), role);
                }
            }
            if(inputStokBarangCabangTotal == null) {
                System.out.println("Pengajuan tidak ditemukan");
                return false;
            }
            if(inputStokBarangCabangTotal.getStep() == 1 && role.equals("Kepala Departemen SDM dan Umum")) {
                if(request.getStatus()) {
                    inputStokBarangCabangTotal.setStep(2);
                    inputStokBarangCabangTotal.setTotalHarga(inputStokBarangCabangTotal.getTotalHarga() );
                    inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Kepala Departemen SDM dan Umum");
                    notifikasiRequestDTO.setRolePenerima("Staf keuangan");
                    notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang telah disetujui");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
                    return true;
                } else {
                    inputStokBarangCabangTotal.setStep(0);
                    inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Kepala Departemen SDM dan Umum");
                    notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                    notifikasiRequestDTO.setRolePenerima("Kepala Operasional Cabang");
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang untuk pusat minta ditolak");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);

                    return false;
                }

            }
            else if(inputStokBarangCabangTotal.getStep() == 2 && role.equals("Staf keuangan")) {
                if(request.getStatus()) {
                    inputStokBarangCabangTotal.setStep(3);
                    inputStokBarangCabangTotal.setTotalHarga(inputStokBarangCabangTotal.getTotalHarga());
                    inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Staf keuangan");
                    notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                    notifikasiRequestDTO.setRolePenerima("Staf Gudang Pelaksana Umum");
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang telah disetujui");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
                    return true;
                } else {
                    inputStokBarangCabangTotal.setStep(0);
                    inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Staf keuangan");
                    notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                    notifikasiRequestDTO.setRolePenerima("Kepala Operasional Cabang");
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang untuk pusat minta ditolak");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);

                    return false;
                }
            }
            else if(inputStokBarangCabangTotal.getStep() == 3 && role.equals("Staf Gudang Pelaksana Umum")) {
                if(request.getStatus()) {
                    inputStokBarangCabangTotal.setStep(4);
                    inputStokBarangCabangTotal.setTotalHarga(inputStokBarangCabangTotal.getTotalHarga());
                    inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                    NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                    notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    notifikasiRequestDTO.setRolePengirim("Staf Gudang Pelaksana Umum");
                    notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                    notifikasiRequestDTO.setRolePenerima("Kepala Operasional Cabang");
                    notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang telah disetujui");
                    notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
                    return true;
                }
            }
            else if(inputStokBarangCabangTotal.getStep() == 4 && role.equals("Kepala Operasional Cabang")) {
                if(request.getStatus()) {
                    boolean result = updateStokCabang(inputStokBarangCabangTotal.getIdPengajuan());
                    if(result) {
                        inputStokBarangCabangTotal.setStep(5);
                        inputStokBarangCabangTotal.setTotalHarga(inputStokBarangCabangTotal.getTotalHarga());
                        inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

                        NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
                        notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                        notifikasiRequestDTO.setRolePengirim("Kepala Operasional Cabang");
                        notifikasiRequestDTO.setRolePenerima("Staf Gudang Pelaksana Umum");
                        notifikasiRequestDTO.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                        notifikasiRequestDTO.setIsiNotifikasi("Penginputan Stok Barang telah disetujui");
                        notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
                        trenPermintaanBukuService.initializePermintaanBuku(request.getNomorCabang(), inputStokBarangCabangTotal.getIdPengajuan());
                        return true;
                    } else {
                        System.out.println("Stok Barang Pusat tidak ditemukan");
                        return false;
                    }
                }
            } else {
                System.out.println("Role tidak sesuai CKS.");
                return false;
            }
        } catch (Exception exception) {
            System.out.println("Error: " + exception.getMessage());
            return false;
        }
        return false;
    }

    @Override
    public PengadaanCabangAsliTotalResponse inputStokBarangCabang(InputStokBarangCARequest listInputStokBarang) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = new InputStokBarangCabangTotal();
            List<PengadaanCabangAsliResponse> pengadaanCabangAsliResponseList = new ArrayList<>();
            inputStokBarangCabangTotal.setIdPengajuan(generateIdPengajuan(listInputStokBarang.getNomorCabang()));
            inputStokBarangCabangTotal.setNomorCabangAsal("001");
            inputStokBarangCabangTotal.setNomorCabangTujuan(listInputStokBarang.getNomorCabang());
            inputStokBarangCabangTotal.setStep(1);
            inputStokBarangCabangTotal.setWaktuPengajuan(LocalDateTime.now());
            inputStokBarangCabangTotal.setFlagCabang(false);

            double totalHarga = 0L;

            inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

            for (InputStokBarangRequest listInputStokBarangRequest : listInputStokBarang.getListInputBarang()) {
                Integer kodeBarang = listInputStokBarangRequest.getKodeBarang();
                int jumlahInput = listInputStokBarangRequest.getStokInput();
                Integer stokBarangPusatSaatIni;

                List<StokBarang> stokBarangList = getStokBarangByNomorCabang(listInputStokBarang.getNomorCabang());
                System.out.println("Kode Barang: " + kodeBarang + "Jumlah Input: " + jumlahInput);
                List<StokBarang> stokBarangPusatList = getStokBarangByNomorCabang("001");

                StokBarang stokBarang = getStokBarangByKodeBarang(kodeBarang, stokBarangList);
                StokBarang stokBarangPusat = getStokBarangByKodeBarang(kodeBarang, stokBarangPusatList);
                if (stokBarangPusat != null) {
                    stokBarangPusatSaatIni = stokBarangPusat.getStokBarang();
                } else {
                    stokBarangPusatSaatIni = 0;
                }

                if (stokBarang != null) {
                    System.out.println("Stok Barang: " + stokBarang.getKodeBarang());
                    InputStokBarangCabang inputStokBarangCabang = new InputStokBarangCabang();
                    inputStokBarangCabang.setStokBarangCabangSaatIni(stokBarang.getStokBarang());
                    inputStokBarangCabang.setStokBarangPusatSaatIni(stokBarangPusatSaatIni);
                    inputStokBarangCabang.setStokInput(jumlahInput);
                    double hargaTambahanCKS = stokBarang.getHargaBarang() * 0.25;
                    inputStokBarangCabang.setHargaBarang(stokBarang.getHargaBarang() + hargaTambahanCKS);
                    inputStokBarangCabang.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    inputStokBarangCabang.setNamaBarang(stokBarang.getNamaBarang());
                    inputStokBarangCabang.setKodeBarang(String.valueOf(stokBarang.getKodeBarang()));
                    try {
                        inputStokBarangCabangDb.save(inputStokBarangCabang);
                    } catch (Exception e) {
                        e.printStackTrace();
                        System.out.println("Error: " + e.getMessage());
                    }
                    pengadaanCabangAsliResponseList.add(mapToInputStokBarangResponse(inputStokBarangCabang));
                    totalHarga += (stokBarang.getHargaBarang() + hargaTambahanCKS) * jumlahInput;
                } else {
                    Barang barang = getBarangByKodeBarang(kodeBarang);
                    if (barang == null) {
                        System.out.println("Barang tidak ditemukan");
                        return null;
                    }
                    InputStokBarangCabang inputStokBarangCabang = new InputStokBarangCabang();
                    inputStokBarangCabang.setStokBarangCabangSaatIni(0);
                    inputStokBarangCabang.setStokBarangPusatSaatIni(stokBarangPusatSaatIni);
                    inputStokBarangCabang.setStokInput(jumlahInput);
                    double hargaTambahanCKS = barang.getHargaBarang() * 0.25;
                    inputStokBarangCabang.setHargaBarang(barang.getHargaBarang() + hargaTambahanCKS);
                    inputStokBarangCabang.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
                    inputStokBarangCabang.setNamaBarang(barang.getNamaBarang());
                    inputStokBarangCabang.setKodeBarang(String.valueOf(barang.getKodeBarang()));
                    try {
                        inputStokBarangCabangDb.save(inputStokBarangCabang);
                    } catch (Exception e) {
                        e.printStackTrace();
                        System.out.println("Error: " + e.getMessage());
                    }
                    pengadaanCabangAsliResponseList.add(mapToInputStokBarangResponse(inputStokBarangCabang));
                    totalHarga += (barang.getHargaBarang() + hargaTambahanCKS) * jumlahInput;
                }
            }

            inputStokBarangCabangTotal.setTotalHarga(totalHarga);
            inputStokBarangCabangTotalDb.save(inputStokBarangCabangTotal);

            NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
            notifikasiRequestDTO.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
            notifikasiRequestDTO.setRolePengirim("Kepala Operasional Cabang");
            notifikasiRequestDTO.setNomorCabang(listInputStokBarang.getNomorCabang());
            notifikasiRequestDTO.setRolePenerima("Kepala Departemen SDM dan Umum");
            notifikasiRequestDTO.setIsiNotifikasi("Pengajuan Stok Barang Cabang Asli");
            notifikasiRestService.createNotifikasi(notifikasiRequestDTO);

            PengadaanCabangAsliTotalResponse pengadaanCabangAsliTotalResponse = new PengadaanCabangAsliTotalResponse();

            pengadaanCabangAsliTotalResponse.setListInputStokBarang(pengadaanCabangAsliResponseList);
            pengadaanCabangAsliTotalResponse.setNomorCabang(listInputStokBarang.getNomorCabang());
            pengadaanCabangAsliTotalResponse.setIdPengajuan(String.valueOf(inputStokBarangCabangTotal.getIdPengajuan()));
            pengadaanCabangAsliTotalResponse.setTotalHarga(String.valueOf(totalHarga));
            pengadaanCabangAsliTotalResponse.setFlag(inputStokBarangCabangTotal.getFlagCabang());
            pengadaanCabangAsliTotalResponse.setStep(inputStokBarangCabangTotal.getStep());

            return pengadaanCabangAsliTotalResponse;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String generateIdPengajuan( String nomorCabangTujuan) {

        if (nomorCabangTujuan == null || nomorCabangTujuan.trim().isEmpty()) {
            throw new IllegalArgumentException("Nomor Cabang Tujuan tidak boleh kosong.");
        }

        int jumlahPengajuan = inputStokBarangCabangTotalDb.findAll().size() + 1;
        String tipeCabangAsal = "KS";


        if (jumlahPengajuan < 0 || jumlahPengajuan > 9999) {
            throw new IllegalArgumentException("Jumlah Pengajuan harus antara 0 dan 9999.");
        }

        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("PGJ");

        ZoneId zoneId = ZoneId.of("Asia/Jakarta");
        Date tanggalPengajuan = new Date();

        // Ubah Date ke LocalDate dengan zona Jakarta
        LocalDate localDate = tanggalPengajuan.toInstant()
                .atZone(zoneId)
                .toLocalDate();

        // Format menjadi YYYYMMDD
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        stringBuilder.append(localDate.format(dateFormatter));

        // 3. NCX - Nomor Cabang Tujuan
        stringBuilder.append(nomorCabangTujuan.toUpperCase());

        // 4. CB - Cabang Asal
        stringBuilder.append(tipeCabangAsal); // Sudah divalidasi, panjang 2 karakter

        // 5. XXXX - Jumlah Pengajuan (Nomor Urut)
        stringBuilder.append(String.format("%04d", jumlahPengajuan)); // Format 4 digit

        return stringBuilder.toString();
    }
    @Override
    public PengadaanCabangAsliTotalResponse getInputStokBarangByIdCabang(String idPengajuan) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = inputStokBarangCabangTotalDb.findById(idPengajuan).orElse(null);
            if(inputStokBarangCabangTotal == null) {
                System.out.println("Pengajuan tidak ditemukan");
                return null;
            }
            PengadaanCabangAsliTotalResponse pengadaanCabangAsliTotalResponse = new PengadaanCabangAsliTotalResponse();
            pengadaanCabangAsliTotalResponse.setIdPengajuan(inputStokBarangCabangTotal.getIdPengajuan());
            pengadaanCabangAsliTotalResponse.setStep(inputStokBarangCabangTotal.getStep());
            pengadaanCabangAsliTotalResponse.setFlag(inputStokBarangCabangTotal.getFlagCabang());
            pengadaanCabangAsliTotalResponse.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
            pengadaanCabangAsliTotalResponse.setTotalHarga(String.valueOf(inputStokBarangCabangTotal.getTotalHarga()));
            List<InputStokBarangCabang> inputStokBarangCabangList = getInputStokBarangByIdPengajuan(idPengajuan);
            List<PengadaanCabangAsliResponse> pengadaanCabangAsliResponseList = new ArrayList<>();
            assert inputStokBarangCabangList != null;
            for(InputStokBarangCabang inputStokBarangCabang : inputStokBarangCabangList) {
                inputStokBarangCabang.setStokBarangPusatSaatIni(getStokBarangPusatSaatIni(Integer.valueOf(inputStokBarangCabang.getKodeBarang())));
                inputStokBarangCabangDb.save(inputStokBarangCabang);
                PengadaanCabangAsliResponse pengadaanCabangAsliResponse = mapToInputStokBarangResponse(inputStokBarangCabang);
                pengadaanCabangAsliResponseList.add(pengadaanCabangAsliResponse);
            }
            pengadaanCabangAsliTotalResponse.setListInputStokBarang(pengadaanCabangAsliResponseList);
            return pengadaanCabangAsliTotalResponse;
        } catch (Exception e) {
            System.out.println("Terjadi kesalahan");
            return null;
        }
    }

    private Integer getStokBarangPusatSaatIni(Integer kodeBarang) {
        try {
            List<StokBarang> stokBarangPusatList = getStokBarangByNomorCabang("001");
            for (StokBarang stokBarang : stokBarangPusatList) {
                if (stokBarang.getKodeBarang().equals(kodeBarang)) {
                    return stokBarang.getStokBarang();
                }
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
        return 0;
    }

    @Override
    public List<HashMap<String, String>> getPengajuanByCabang(String nomorCabang) {
        List<InputStokBarangCabangTotal> inputStokBarangPusatList = inputStokBarangCabangTotalDb.findAll();
        List<HashMap<String, String>> pengajuanList = new ArrayList<>();
        for (InputStokBarangCabangTotal inputStokBarangPusat : inputStokBarangPusatList) {
            HashMap<String, String> pengajuan = new HashMap<>();
            if(inputStokBarangPusat.getNomorCabangTujuan().equals(nomorCabang)) {
                pengajuan.put("idPengajuan", String.valueOf(inputStokBarangPusat.getIdPengajuan()));
                pengajuan.put("step", String.valueOf(inputStokBarangPusat.getStep()));
                pengajuan.put("nomorCabangTujuan", inputStokBarangPusat.getNomorCabangTujuan());
                pengajuan.put("nomorCabangAsal", inputStokBarangPusat.getNomorCabangAsal());
                SimpleDateFormat formatter = new SimpleDateFormat("dd : MMMMM : yyyy HH:mm", new Locale("id", "ID"));
                String waktuPengajuan = formatter.format(inputStokBarangPusat.getWaktuPengajuan());
                pengajuan.put("waktuPengajuan", waktuPengajuan);
                pengajuanList.add(pengajuan);
            }
        }

        return pengajuanList;

    }

    @Override
    public Boolean revisiInputStokBarangCabang(List<InputStokBarangRequest> listInputStokBarang, String nomorCabang, String idPengajuan) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = inputStokBarangCabangTotalDb.findById(idPengajuan).orElse(null);
            if(inputStokBarangCabangTotal == null) {
                System.out.println("Pengajuan tidak ditemukan");
                return false;
            }
            inputStokBarangCabangTotal.setStep(5);
            List<StokBarang> stokBarangList = getStokBarangByNomorCabang(nomorCabang);
            List<StokBarang> stokBarangPusatList = getStokBarangByNomorCabang("001");
            for(InputStokBarangRequest inputStokBarangRequest: listInputStokBarang) {
                StokBarang stokBarang = getStokBarangByKodeBarang(inputStokBarangRequest.getKodeBarang(), stokBarangList);
                StokBarang stokBarangPusat = getStokBarangByKodeBarang(inputStokBarangRequest.getKodeBarang(), stokBarangPusatList);
                if(stokBarang != null) {
                    Integer stokSaatIni = stokBarang.getStokBarang();
                    assert stokBarangPusat != null;
                    Integer stokPusatSaatIni = stokBarangPusat.getStokBarang();
                    stokBarang.setStokBarang(inputStokBarangRequest.getStokInput() + stokSaatIni);
                    stokBarangPusat.setStokBarang(stokPusatSaatIni - inputStokBarangRequest.getStokInput());
                    stokBarangRepository.save(stokBarang);
                    logStokBarangService.createLogStokBarang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang(), stokSaatIni, stokBarang.getStokBarang(), idPengajuan, "PCKS");

                } else {
                    Barang barang = getBarangByKodeBarang(inputStokBarangRequest.getKodeBarang());
                    if(barang == null) {
                        System.out.println("Barang tidak ditemukan");
                        return false;
                    }
                    assert stokBarangPusat != null;
                    Integer stokPusatSaatIni = stokBarangPusat.getStokBarang();
                    StokBarang stokBarangBaru = new StokBarang();
                    stokBarangBaru.setKodeBarang(inputStokBarangRequest.getKodeBarang());
                    stokBarangBaru.setNamaBarang(barang.getNamaBarang());
                    stokBarangBaru.setNomorCabang(nomorCabang);
                    stokBarangBaru.setStokBarang(inputStokBarangRequest.getStokInput());
                    stokBarangPusat.setStokBarang(stokPusatSaatIni - inputStokBarangRequest.getStokInput());
                    double hargaTambahanCKS = barang.getHargaBarang() * 0.25;
                    stokBarangBaru.setHargaBarang(barang.getHargaBarang() + hargaTambahanCKS);
                    stokBarangRepository.save(stokBarangBaru);
                    logStokBarangService.createLogStokBarang(stokBarangBaru.getKodeBarang(), stokBarangBaru.getNomorCabang(), 0, stokBarangBaru.getStokBarang(), idPengajuan, "PCKS");

                }
            }
            NotifikasiRequestDTO notifikasiRequestDTO = new NotifikasiRequestDTO();
            notifikasiRequestDTO.setIdPengajuan(idPengajuan);
            notifikasiRequestDTO.setRolePengirim("Kepala Operasional Cabang");
            notifikasiRequestDTO.setNomorCabang(nomorCabang);
            notifikasiRequestDTO.setRolePenerima("Kepala Departemen SDM dan Umum");
            notifikasiRequestDTO.setIsiNotifikasi("Terdapat revisi pengadaan stok barang cabang kerja sama");
            notifikasiRestService.createNotifikasi(notifikasiRequestDTO);
            return true;
        }
        catch (Exception e) {
            System.out.println("Terjadi kesalahan");
            return false;
        }
    }

    private List<StokBarang> getStokBarangByNomorCabang(String nomorCabang) {
        List<StokBarang> stokBarangList = stokBarangRepository.findAll();
        List<StokBarang> result = new ArrayList<>();
        for (StokBarang stokBarang : stokBarangList) {
            if (stokBarang.getNomorCabang().equals(nomorCabang)) {
                result.add(stokBarang);
            }
        }
        return result;
    }

    private StokBarang getStokBarangByKodeBarang(Integer kodeBarang, List<StokBarang> stokBarangList) {
        for (StokBarang stokBarang : stokBarangList) {
            if (stokBarang.getKodeBarang().equals(kodeBarang)) {
                return stokBarang;
            }
        }
        return null;
    }

    private PengadaanCabangAsliResponse mapToInputStokBarangResponse(InputStokBarangCabang inputStokBarangCabang) {
        PengadaanCabangAsliResponse response = new PengadaanCabangAsliResponse();
        response.setKodeBarang(inputStokBarangCabang.getKodeBarang());
        response.setNamaBarang(inputStokBarangCabang.getNamaBarang());
        response.setStokSaatIni(String.valueOf(inputStokBarangCabang.getStokBarangCabangSaatIni()));
        response.setStokPusatSaatIni(String.valueOf(inputStokBarangCabang.getStokBarangPusatSaatIni()));
        response.setStokInput(String.valueOf(inputStokBarangCabang.getStokInput()));
        System.out.println("response: " + response.getStokInput());
        response.setHargaBarang(String.valueOf(inputStokBarangCabang.getHargaBarang()));

        return response;
    }

    private Barang getBarangByKodeBarang(Integer kodeBarang) {
        List<Barang> barangList = barangDb.findAll();
        for(Barang barang : barangList) {
            if(barang.getKodeBarang().equals(kodeBarang)) {
                return barang;
            }
        }
        return null;
    }

    private List<InputStokBarangCabang> getInputStokBarangByIdPengajuan(String idPengajuan) {
        try {
            List<InputStokBarangCabang> inputStokBarangCabangList = inputStokBarangCabangDb.findAll();
            List<InputStokBarangCabang> result = new ArrayList<>();
            for(InputStokBarangCabang inputStokBarangCabang: inputStokBarangCabangList) {
                if(inputStokBarangCabang.getIdPengajuan().equals(idPengajuan)) {
                    result.add(inputStokBarangCabang);
                }
            }
            return result;
        } catch (Exception e) {
            System.out.println("Pengajuan Tidak Ditemukan " + e.getMessage());
            return null;
        }

    }

    private boolean updateStokCabang(String idPengajuan) {
        try {
            InputStokBarangCabangTotal inputStokBarangCabangTotal = inputStokBarangCabangTotalDb.findById(idPengajuan).orElse(null);
            if (inputStokBarangCabangTotal == null) {
                System.out.println("Pengajuan tidak ditemukan");
                return false;
            }
            List<InputStokBarangCabang> inputStokBarangCabangList = getInputStokBarangByIdPengajuan(idPengajuan);

            List<StokBarang> stokBarangList = getStokBarangByNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
            List<StokBarang> stokBarangPusatList = getStokBarangByNomorCabang("001");
            if(stokBarangList.isEmpty()) {
                assert inputStokBarangCabangList != null;
                for(InputStokBarangCabang inputStokBarangCabang : inputStokBarangCabangList) {
                    StokBarang stokBarang = new StokBarang();
                    StokBarang stokBarangPusat = getStokBarangByKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()), stokBarangPusatList);
                    if(stokBarangPusat != null) {
                        Barang barang = barangDb.findById(Integer.valueOf(inputStokBarangCabang.getKodeBarang())).orElse(null);
                        if(barang != null) {
                            stokBarang.setKategoriBarang(barang.getKategoriBarang());
                            stokBarang.setBentuk(barang.getBentuk());
                        }
                        stokBarang.setKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()));
                        stokBarang.setNamaBarang(inputStokBarangCabang.getNamaBarang());
                        stokBarang.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                        stokBarang.setStokBarang(inputStokBarangCabang.getStokInput());
                        stokBarang.setHargaBarang(inputStokBarangCabang.getHargaBarang());

                        stokBarangRepository.save(stokBarang);
                        logStokBarangService.createLogStokBarang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang(), 0, stokBarang.getStokBarang(), inputStokBarangCabang.getIdPengajuan(), "PCKS");

                    } else {
                        return false;
                    }
                }
                return true;
            } else {
                assert inputStokBarangCabangList != null;
                for(InputStokBarangCabang inputStokBarangCabang : inputStokBarangCabangList) {
                    StokBarang stokBarang = getStokBarangByKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()), stokBarangList);
                    StokBarang stokBarangPusat = getStokBarangByKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()), stokBarangPusatList);
                    if(stokBarangPusat != null) {
                        if(stokBarang != null) {
                            Integer stokSebelum = stokBarang.getStokBarang();
                            stokBarang.setStokBarang(stokBarang.getStokBarang() + inputStokBarangCabang.getStokInput());
                            stokBarangPusat.setStokBarang(stokBarangPusat.getStokBarang() - inputStokBarangCabang.getStokInput());
                            stokBarangRepository.save(stokBarang);
                            logStokBarangService.createLogStokBarang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang(), stokSebelum, stokBarang.getStokBarang(), inputStokBarangCabang.getIdPengajuan(), "PCKS");

                        } else {
                            StokBarang stokBarangBaru = new StokBarang();
                            Barang barang = barangDb.findById(Integer.valueOf(inputStokBarangCabang.getKodeBarang())).orElse(null);
                            if(barang != null) {
                                stokBarangBaru.setKategoriBarang(barang.getKategoriBarang());
                                stokBarangBaru.setBentuk(barang.getBentuk());
                            }
                            stokBarangBaru.setKodeBarang(Integer.valueOf(inputStokBarangCabang.getKodeBarang()));
                            stokBarangBaru.setNamaBarang(inputStokBarangCabang.getNamaBarang());
                            stokBarangBaru.setNomorCabang(inputStokBarangCabangTotal.getNomorCabangTujuan());
                            stokBarangBaru.setStokBarang(inputStokBarangCabang.getStokInput());
                            stokBarangBaru.setHargaBarang(inputStokBarangCabang.getHargaBarang());
                            stokBarangPusat.setStokBarang(stokBarangPusat.getStokBarang() - inputStokBarangCabang.getStokInput());
                            stokBarangRepository.save(stokBarangBaru);
                            logStokBarangService.createLogStokBarang(stokBarangBaru.getKodeBarang(), stokBarangBaru.getNomorCabang(), 0, stokBarangBaru.getStokBarang(), inputStokBarangCabang.getIdPengajuan(), "PCKS");

                        }
                    } else {
                        System.out.println("Stok Barang Pusat tidak ditemukan");
                        return false;
                    }
                }
                return true;
            }
        } catch (Exception e) {
            System.out.println("Terjadi Error");
            return false;
        }
    }
}
