package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.KurangStokBarangListRequest;
import lblia.propensi.siinven.dto.response.StokBarangResponseDTO;
import lblia.propensi.siinven.model.StokBarang;

import java.util.List;

public interface KurangStokBarangService {
    Integer kurangiStokBarang(KurangStokBarangListRequest listInputStokBarang);
    List<StokBarangResponseDTO> getStokBarangByCabang(String nomorCabang);

}
