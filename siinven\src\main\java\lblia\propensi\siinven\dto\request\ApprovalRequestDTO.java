package lblia.propensi.siinven.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalRequestDTO {
    
    @NotBlank(message = "Status approval harus diisi")
    @Pattern(regexp = "DISETUJUI|DITOLAK", 
             message = "Status approval harus DISETUJUI atau DITOLAK")
    private String statusApproval;
}