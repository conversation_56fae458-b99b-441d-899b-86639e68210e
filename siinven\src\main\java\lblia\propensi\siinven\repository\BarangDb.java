package lblia.propensi.siinven.repository;

import lblia.propensi.siinven.model.Barang;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BarangDb extends JpaRepository<Barang, Integer> {
    @Query("SELECT c FROM Barang c WHERE c.deletedAt IS NULL")
    List<Barang> findAllActiveBranches();

    @Query("SELECT b FROM Barang b WHERE b.namaBarang = :namaBarang AND b.deletedAt IS NULL")
    Barang findByNamaBarang(@Param("namaBarang") String namaBarang);

    @Query("SELECT b FROM Barang b WHERE b.kodeBarang = :kodeBarang AND b.deletedAt IS NULL")
    Barang findActiveByKodeBarang(@Param("kodeBarang") Integer kodeBarang);

}