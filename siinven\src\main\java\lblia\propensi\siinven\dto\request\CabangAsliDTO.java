package lblia.propensi.siinven.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Time;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CabangAsliDTO {
    private String nomorCabang;  
    private String namaCabang;
    private String alamat;
    private String kontak;
    private Integer jumlahKaryawan;
    private Time jamOperasional;
    private String idKepalaOperasional;
}